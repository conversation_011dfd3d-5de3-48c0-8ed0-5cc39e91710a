[Unit]
Description=Driver Management System Task Scheduler
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/driver-management-system
Environment=PATH=/path/to/driver-management-system/venv/bin
Environment=PYTHONPATH=/path/to/driver-management-system
ExecStart=/path/to/driver-management-system/venv/bin/python run_scheduler.py start
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=driver-scheduler

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/path/to/driver-management-system

[Install]
WantedBy=multi-user.target