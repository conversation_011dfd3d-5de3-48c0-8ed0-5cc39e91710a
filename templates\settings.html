<!-- templates/settings.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>الإعدادات</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <a href="{{ url_for('dashboard') }}" class="btn btn-light me-2">السائقون</a>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light active">الإعدادات</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="card shadow-sm">
    <div class="card-body">
      <h4 class="mb-3">تغيير كلمة المرور</h4>
      <form method="post" novalidate>
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label">كلمة المرور الحالية</label>
            <input type="password" name="current_password" class="form-control" required>
          </div>
          <div class="col-md-4">
            <label class="form-label">كلمة المرور الجديدة</label>
            <input type="password" name="new_password" class="form-control" required>
          </div>
          <div class="col-md-4">
            <label class="form-label">تأكيد كلمة المرور</label>
            <input type="password" name="confirm_password" class="form-control" required>
          </div>
        </div>
        <div class="mt-3">
          <button type="submit" class="btn btn-primary">تحديث</button>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">عودة</a>
        </div>
      </form>
    </div>
  </div>

  <!-- النسخ الاحتياطي -->
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">النسخ الاحتياطي وصيانة البيانات</h5>
      <p class="text-muted">يمكنك إنشاء نسخة احتياطية من قاعدة البيانات وفحص تكامل البيانات.</p>
      <div class="d-flex gap-2 flex-wrap">
        <a href="{{ url_for('backup_database') }}" class="btn btn-info">تحميل نسخة احتياطية</a>
        <a href="{{ url_for('data_integrity') }}" class="btn btn-warning">فحص تكامل البيانات</a>
        <form method="post" action="{{ url_for('seed_data') }}" class="d-inline">
          <button type="submit" class="btn btn-secondary" onclick="return confirm('هل تريد إنشاء بيانات تجريبية؟ سيتم إضافة سائقين ومركبات ومدفوعات تجريبية.')">إنشاء بيانات تجريبية</button>
        </form>
      </div>
    </div>
  </div>

  <!-- معلومات النظام -->
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">معلومات النظام</h5>
      <dl class="row">
        <dt class="col-sm-3">إصدار النظام</dt>
        <dd class="col-sm-9">1.0.0</dd>
        <dt class="col-sm-3">قاعدة البيانات</dt>
        <dd class="col-sm-9">SQLite</dd>
        <dt class="col-sm-3">آخر تحديث</dt>
        <dd class="col-sm-9">{{ moment().format('YYYY-MM-DD HH:mm:ss') if moment else 'غير متاح' }}</dd>
      </dl>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>