# app.py
# تطبيق Flask مع SQLAlchemy: نماذج وإدارة سائقين + جلسات وتسجيل دخول
# يشمل النماذج المطلوبة: Driver, Vehicle, Salary, Payment, Expense, User, Role
# ملاحظات:
# - يتم إنشاء/ترحيل قاعدة البيانات تلقائيًا عند التشغيل الأول (إضافة role_id إلى users إن لم يكن موجودًا)
# - يوجد مستخدم افتراضي: admin / admin123 (قم بتغييره فورًا للإنتاج)

import os
import sqlite3
from datetime import datetime, date
from functools import wraps
from flask import (
    Flask, render_template, request, redirect, url_for,
    session, flash, send_file, make_response, jsonify
)
from werkzeug.security import generate_password_hash, check_password_hash
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import func, text
import io
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill

BASE_DIR = os.path.dirname(__file__)
DATABASE_PATH = os.path.join(BASE_DIR, "app.db")

app = Flask(__name__)
app.secret_key = "CHANGE_THIS_SECRET_KEY"  # غيّر هذا المفتاح في الإنتاج

# إعداد SQLAlchemy
app.config["SQLALCHEMY_DATABASE_URI"] = f"sqlite:///{DATABASE_PATH}"
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

db = SQLAlchemy(app)

# إضافة filters مخصصة لـ Jinja2
@app.template_filter('from_json')
def from_json_filter(value):
    """تحويل JSON string إلى Python object"""
    if not value:
        return []
    try:
        return json.loads(value)
    except:
        return []

# تهيئة نظام الإشعارات
try:
    from notifications import init_notifications
    notification_manager = init_notifications(app)
except ImportError:
    notification_manager = None

# تهيئة الـ API
try:
    from api import init_api
    init_api(app)
except ImportError:
    pass

# تهيئة نظام المراقبة
try:
    from monitoring import init_monitoring
    system_monitor = init_monitoring(app)
    # بدء المراقبة
    system_monitor.start_monitoring(interval=300)  # كل 5 دقائق
except ImportError:
    system_monitor = None

# تهيئة نظام إدارة المستخدمين
try:
    from user_management import init_user_management
    user_manager = init_user_management(app)
except ImportError:
    user_manager = None

# تهيئة مدير النسخ الاحتياطية
try:
    from backup_manager import init_backup_manager
    backup_manager = init_backup_manager(app)
except ImportError:
    backup_manager = None

# تهيئة مدير الملفات
try:
    from file_manager import init_file_manager
    file_manager = init_file_manager(app)
except ImportError:
    file_manager = None

# -------------------------------
# النماذج (Models)
# -------------------------------
class Role(db.Model):
    __tablename__ = "roles"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    display_name = db.Column(db.String(100), nullable=True)
    description = db.Column(db.Text, nullable=True)
    permissions = db.Column(db.Text, nullable=True)  # JSON string
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<Role {self.name}>"


class User(db.Model):
    __tablename__ = "users"
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    full_name = db.Column(db.String(200), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=True)
    # ملاحظة: سيتم إضافة العمود تلقائيًا بعملية ترحيل بسيطة إن لم يكن موجودًا
    role_id = db.Column(db.Integer, db.ForeignKey("roles.id"), nullable=True)
    role = db.relationship("Role", backref="users")

    def set_password(self, password: str):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password: str) -> bool:
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f"<User {self.username}>"


class Driver(db.Model):
    __tablename__ = "drivers"
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(120), nullable=False)
    phone = db.Column(db.String(50), nullable=False)
    balance = db.Column(db.Float, nullable=False, default=0.0)

    vehicles = db.relationship("Vehicle", backref="driver", lazy=True)
    salaries = db.relationship("Salary", backref="driver", lazy=True)
    payments = db.relationship("Payment", backref="driver", lazy=True)
    expenses = db.relationship("Expense", backref="driver", lazy=True)

    def __repr__(self):
        return f"<Driver {self.name}>"


class Vehicle(db.Model):
    __tablename__ = "vehicles"
    id = db.Column(db.Integer, primary_key=True)
    plate_number = db.Column(db.String(50), unique=True, nullable=False)
    model = db.Column(db.String(100), nullable=True)
    driver_id = db.Column(db.Integer, db.ForeignKey("drivers.id"), nullable=True)

    def __repr__(self):
        return f"<Vehicle {self.plate_number}>"


class Salary(db.Model):
    __tablename__ = "salaries"
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey("drivers.id"), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    period_start = db.Column(db.Date, nullable=True)
    period_end = db.Column(db.Date, nullable=True)
    paid_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<Salary driver={self.driver_id} amount={self.amount}>"


class Payment(db.Model):
    __tablename__ = "payments"
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey("drivers.id"), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    direction = db.Column(db.String(10), nullable=False, default="in")  # in/out
    method = db.Column(db.String(50), nullable=True)  # cash, transfer, ...
    note = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f"<Payment driver={self.driver_id} {self.direction} {self.amount}>"


class Expense(db.Model):
    __tablename__ = "expenses"
    id = db.Column(db.Integer, primary_key=True)
    driver_id = db.Column(db.Integer, db.ForeignKey("drivers.id"), nullable=True)
    vehicle_id = db.Column(db.Integer, db.ForeignKey("vehicles.id"), nullable=True)
    amount = db.Column(db.Float, nullable=False)
    category = db.Column(db.String(100), nullable=True)
    note = db.Column(db.String(255), nullable=True)
    occurred_at = db.Column(db.DateTime, default=datetime.utcnow)

    vehicle = db.relationship("Vehicle", backref="expenses")

    def __repr__(self):
        return f"<Expense amount={self.amount} category={self.category}>"


# -------------------------------
# ترحيل مبسّط لقاعدة البيانات (SQLite)
# -------------------------------

def migrate_sqlite_schema():
    """ترحيل بسيط لإضافة عمود role_id إلى users إن لم يكن موجودًا."""
    if not os.path.exists(DATABASE_PATH):
        return
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cur = conn.cursor()
        cur.execute("PRAGMA table_info(users);")
        cols = [row[1] for row in cur.fetchall()]
        if "role_id" not in cols:
            cur.execute("ALTER TABLE users ADD COLUMN role_id INTEGER;")
            conn.commit()
    except Exception as e:
        print("Migration warning:", e)
    finally:
        try:
            conn.close()
        except Exception:
            pass


# -------------------------------
# أدوات المساعدة
# -------------------------------

def login_required(view_func):
    """ديكوريتر لمنع الوصول بدون تسجيل الدخول"""
    @wraps(view_func)
    def wrapped_view(*args, **kwargs):
        if "user_id" not in session:
            flash("الرجاء تسجيل الدخول أولاً.", "warning")
            return redirect(url_for("login"))
        return view_func(*args, **kwargs)
    return wrapped_view


# -------------------------------
# المسارات العامة
# -------------------------------
@app.route("/")
def index():
    if "user_id" in session:
        return redirect(url_for("dashboard"))
    return redirect(url_for("login"))


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form.get("username", "").strip()
        password = request.form.get("password", "")
        if not username or not password:
            flash("يرجى إدخال اسم المستخدم وكلمة المرور.", "danger")
            return render_template("login.html")
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            session["user_id"] = user.id
            session["username"] = user.username
            flash("تم تسجيل الدخول بنجاح.", "success")
            return redirect(url_for("dashboard"))
        flash("بيانات الدخول غير صحيحة.", "danger")
    return render_template("login.html")


@app.route("/logout")
@login_required
def logout():
    session.clear()
    flash("تم تسجيل الخروج.", "info")
    return redirect(url_for("login"))


# -------------------------------
# لوحة التحكم وإدارة السائقين
# -------------------------------
@app.route("/dashboard")
@login_required
def dashboard():
    search = request.args.get('search', '').strip()
    balance_status = request.args.get("balance_status", "")
    sort_by = request.args.get("sort_by", "name")
    
    query = Driver.query
    
    # فلترة البحث
    if search:
        query = query.filter(
            db.or_(
                Driver.name.contains(search),
                Driver.phone.contains(search)
            )
        )
    
    # فلترة حالة الرصيد
    if balance_status == "positive":
        query = query.filter(Driver.balance > 0)
    elif balance_status == "negative":
        query = query.filter(Driver.balance < 0)
    elif balance_status == "zero":
        query = query.filter(Driver.balance == 0)
    
    # الترتيب
    if sort_by == "balance_desc":
        query = query.order_by(Driver.balance.desc())
    elif sort_by == "balance_asc":
        query = query.order_by(Driver.balance.asc())
    elif sort_by == "created_desc":
        query = query.order_by(Driver.id.desc())
    else:  # name
        query = query.order_by(Driver.name)
    
    drivers = query.all()
    
    # إحصائيات سريعة للإشعارات
    notifications = []
    
    # سائقون برصيد سالب كبير
    high_debt_drivers = Driver.query.filter(Driver.balance < -1000).count()
    if high_debt_drivers > 0:
        notifications.append({
            'type': 'warning',
            'message': f'يوجد {high_debt_drivers} سائق برصيد سالب أكثر من 1000',
            'icon': 'fas fa-exclamation-triangle'
        })
    
    # سائقون بدون مركبات
    drivers_without_vehicles = db.session.query(Driver).outerjoin(Vehicle).filter(Vehicle.id.is_(None)).count()
    if drivers_without_vehicles > 0:
        notifications.append({
            'type': 'info',
            'message': f'يوجد {drivers_without_vehicles} سائق بدون مركبات',
            'icon': 'fas fa-info-circle'
        })
    
    return render_template("dashboard.html", 
                         drivers=drivers, 
                         search=search,
                         notifications=notifications)


@app.route("/drivers/add", methods=["GET", "POST"])
@login_required
def add_driver():
    if request.method == "POST":
        name = request.form.get("name", "").strip()
        phone = request.form.get("phone", "").strip()
        balance = request.form.get("balance", "0").strip() or "0"
        if not name or not phone:
            flash("يرجى إدخال الاسم ورقم الهاتف.", "danger")
            return render_template("add_driver.html")
        try:
            balance_val = float(balance)
        except ValueError:
            flash("الرجاء إدخال رصيد صالح (رقم).", "danger")
            return render_template("add_driver.html")
        d = Driver(name=name, phone=phone, balance=balance_val)
        db.session.add(d)
        db.session.commit()
        flash("تمت إضافة السائق بنجاح.", "success")
        return redirect(url_for("dashboard"))
    return render_template("add_driver.html")


@app.route("/drivers/edit/<int:driver_id>", methods=["GET", "POST"])
@login_required
def edit_driver(driver_id):
    driver = Driver.query.get(driver_id)
    if not driver:
        flash("السائق غير موجود.", "warning")
        return redirect(url_for("dashboard"))
    if request.method == "POST":
        name = request.form.get("name", "").strip()
        phone = request.form.get("phone", "").strip()
        balance = request.form.get("balance", "0").strip() or "0"
        if not name or not phone:
            flash("يرجى إدخال الاسم ورقم الهاتف.", "danger")
            return render_template("edit_driver.html", driver=driver)
        try:
            balance_val = float(balance)
        except ValueError:
            flash("الرجاء إدخال رصيد صالح (رقم).", "danger")
            return render_template("edit_driver.html", driver=driver)
        driver.name = name
        driver.phone = phone
        driver.balance = balance_val
        db.session.commit()
        flash("تم تحديث بيانات السائق.", "success")
        return redirect(url_for("dashboard"))
    return render_template("edit_driver.html", driver=driver)


@app.route("/drivers/delete/<int:driver_id>", methods=["POST"])
@login_required
def delete_driver(driver_id):
    driver = Driver.query.get(driver_id)
    if not driver:
        flash("السائق غير موجود.", "warning")
        return redirect(url_for("dashboard"))
    db.session.delete(driver)
    db.session.commit()
    flash("تم حذف السائق.", "info")
    return redirect(url_for("dashboard"))


# -------------------------------
# إدارة المركبات
# -------------------------------
@app.route("/vehicles")
@login_required
def vehicles():
    vehicles = Vehicle.query.order_by(Vehicle.id.desc()).all()
    return render_template("vehicles.html", vehicles=vehicles)


@app.route("/vehicles/add", methods=["GET", "POST"])
@login_required
def add_vehicle():
    if request.method == "POST":
        plate_number = request.form.get("plate_number", "").strip()
        model = request.form.get("model", "").strip()
        driver_id = request.form.get("driver_id", "").strip()
        
        if not plate_number:
            flash("يرجى إدخال رقم اللوحة.", "danger")
            drivers = Driver.query.all()
            return render_template("add_vehicle.html", drivers=drivers)
        
        # التحقق من عدم تكرار رقم اللوحة
        existing = Vehicle.query.filter_by(plate_number=plate_number).first()
        if existing:
            flash("رقم اللوحة موجود مسبقاً.", "danger")
            drivers = Driver.query.all()
            return render_template("add_vehicle.html", drivers=drivers)
        
        driver_id_val = None
        if driver_id:
            try:
                driver_id_val = int(driver_id)
                if not Driver.query.get(driver_id_val):
                    flash("السائق المحدد غير موجود.", "danger")
                    drivers = Driver.query.all()
                    return render_template("add_vehicle.html", drivers=drivers)
            except ValueError:
                flash("معرف السائق غير صالح.", "danger")
                drivers = Driver.query.all()
                return render_template("add_vehicle.html", drivers=drivers)
        
        vehicle = Vehicle(plate_number=plate_number, model=model, driver_id=driver_id_val)
        db.session.add(vehicle)
        db.session.commit()
        flash("تمت إضافة المركبة بنجاح.", "success")
        return redirect(url_for("vehicles"))
    
    drivers = Driver.query.all()
    return render_template("add_vehicle.html", drivers=drivers)


@app.route("/vehicles/edit/<int:vehicle_id>", methods=["GET", "POST"])
@login_required
def edit_vehicle(vehicle_id):
    vehicle = Vehicle.query.get(vehicle_id)
    if not vehicle:
        flash("المركبة غير موجودة.", "warning")
        return redirect(url_for("vehicles"))
    
    if request.method == "POST":
        plate_number = request.form.get("plate_number", "").strip()
        model = request.form.get("model", "").strip()
        driver_id = request.form.get("driver_id", "").strip()
        
        if not plate_number:
            flash("يرجى إدخال رقم اللوحة.", "danger")
            drivers = Driver.query.all()
            return render_template("edit_vehicle.html", vehicle=vehicle, drivers=drivers)
        
        # التحقق من عدم تكرار رقم اللوحة (باستثناء المركبة الحالية)
        existing = Vehicle.query.filter(Vehicle.plate_number == plate_number, Vehicle.id != vehicle_id).first()
        if existing:
            flash("رقم اللوحة موجود مسبقاً.", "danger")
            drivers = Driver.query.all()
            return render_template("edit_vehicle.html", vehicle=vehicle, drivers=drivers)
        
        driver_id_val = None
        if driver_id:
            try:
                driver_id_val = int(driver_id)
                if not Driver.query.get(driver_id_val):
                    flash("السائق المحدد غير موجود.", "danger")
                    drivers = Driver.query.all()
                    return render_template("edit_vehicle.html", vehicle=vehicle, drivers=drivers)
            except ValueError:
                flash("معرف السائق غير صالح.", "danger")
                drivers = Driver.query.all()
                return render_template("edit_vehicle.html", vehicle=vehicle, drivers=drivers)
        
        vehicle.plate_number = plate_number
        vehicle.model = model
        vehicle.driver_id = driver_id_val
        db.session.commit()
        flash("تم تحديث بيانات المركبة.", "success")
        return redirect(url_for("vehicles"))
    
    drivers = Driver.query.all()
    return render_template("edit_vehicle.html", vehicle=vehicle, drivers=drivers)


@app.route("/vehicles/delete/<int:vehicle_id>", methods=["POST"])
@login_required
def delete_vehicle(vehicle_id):
    vehicle = Vehicle.query.get(vehicle_id)
    if not vehicle:
        flash("المركبة غير موجودة.", "warning")
        return redirect(url_for("vehicles"))
    
    db.session.delete(vehicle)
    db.session.commit()
    flash("تم حذف المركبة.", "info")
    return redirect(url_for("vehicles"))


# -------------------------------
# إدارة المدفوعات
# -------------------------------
@app.route("/payments")
@login_required
def payments():
    # فلترة المدفوعات
    driver_id = request.args.get('driver_id')
    direction = request.args.get('direction')
    method = request.args.get('method')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    query = Payment.query
    
    if driver_id:
        query = query.filter(Payment.driver_id == driver_id)
    if direction:
        query = query.filter(Payment.direction == direction)
    if method:
        query = query.filter(Payment.method == method)
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Payment.created_at >= date_from_obj)
        except ValueError:
            pass
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Payment.created_at <= date_to_obj)
        except ValueError:
            pass
    
    # الترقيم
    page = request.args.get('page', 1, type=int)
    per_page = 20  # عدد العناصر في كل صفحة
    
    payments_paginated = query.order_by(Payment.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    payments = payments_paginated.items
    
    drivers = Driver.query.order_by(Driver.name).all()
    
    # إحصائيات المدفوعات المفلترة (من جميع النتائج، ليس فقط الصفحة الحالية)
    all_filtered_payments = query.all()
    total_in = sum(p.amount for p in all_filtered_payments if p.direction == 'in')
    total_out = sum(p.amount for p in all_filtered_payments if p.direction == 'out')
    
    return render_template("payments.html", 
                         payments=payments, 
                         drivers=drivers,
                         total_in=total_in,
                         total_out=total_out,
                         pagination=payments_paginated,
                         filters={
                             'driver_id': driver_id,
                             'direction': direction,
                             'method': method,
                             'date_from': date_from,
                             'date_to': date_to
                         })


@app.route("/payments/add", methods=["GET", "POST"])
@login_required
def add_payment():
    if request.method == "POST":
        driver_id = request.form.get("driver_id", "").strip()
        amount = request.form.get("amount", "").strip()
        direction = request.form.get("direction", "in")
        method = request.form.get("method", "").strip()
        note = request.form.get("note", "").strip()
        
        if not driver_id or not amount:
            flash("يرجى إدخال السائق والمبلغ.", "danger")
            drivers = Driver.query.all()
            return render_template("add_payment.html", drivers=drivers)
        
        try:
            driver_id_val = int(driver_id)
            amount_val = float(amount)
        except ValueError:
            flash("يرجى إدخال قيم صالحة.", "danger")
            drivers = Driver.query.all()
            return render_template("add_payment.html", drivers=drivers)
        
        driver = Driver.query.get(driver_id_val)
        if not driver:
            flash("السائق غير موجود.", "danger")
            drivers = Driver.query.all()
            return render_template("add_payment.html", drivers=drivers)
        
        # إضافة المدفوعة
        payment = Payment(
            driver_id=driver_id_val,
            amount=amount_val,
            direction=direction,
            method=method,
            note=note
        )
        db.session.add(payment)
        
        # تحديث رصيد السائق
        if direction == "in":
            driver.balance += amount_val
        else:
            driver.balance -= amount_val
        
        db.session.commit()
        flash("تمت إضافة المدفوعة بنجاح.", "success")
        return redirect(url_for("payments"))
    
    # التحقق من وجود driver_id في الرابط
    selected_driver_id = request.args.get('driver_id')
    drivers = Driver.query.all()
    return render_template("add_payment.html", drivers=drivers, selected_driver_id=selected_driver_id)


@app.route("/payments/delete/<int:payment_id>", methods=["POST"])
@login_required
def delete_payment(payment_id):
    payment = Payment.query.get(payment_id)
    if not payment:
        flash("المدفوعة غير موجودة.", "warning")
        return redirect(url_for("payments"))
    
    # عكس تأثير المدفوعة على رصيد السائق
    driver = payment.driver
    if payment.direction == "in":
        driver.balance -= payment.amount
    else:
        driver.balance += payment.amount
    
    db.session.delete(payment)
    db.session.commit()
    flash("تم حذف المدفوعة.", "info")
    return redirect(url_for("payments"))


# -------------------------------
# إدارة الرواتب
# -------------------------------
@app.route("/salaries")
@login_required
def salaries():
    salaries = Salary.query.order_by(Salary.paid_at.desc()).all()
    return render_template("salaries.html", salaries=salaries)


@app.route("/salaries/add", methods=["GET", "POST"])
@login_required
def add_salary():
    if request.method == "POST":
        driver_id = request.form.get("driver_id", "").strip()
        amount = request.form.get("amount", "").strip()
        period_start = request.form.get("period_start", "").strip()
        period_end = request.form.get("period_end", "").strip()
        
        if not driver_id or not amount:
            flash("يرجى إدخال السائق والمبلغ.", "danger")
            drivers = Driver.query.all()
            return render_template("add_salary.html", drivers=drivers)
        
        try:
            driver_id_val = int(driver_id)
            amount_val = float(amount)
        except ValueError:
            flash("يرجى إدخال قيم صالحة.", "danger")
            drivers = Driver.query.all()
            return render_template("add_salary.html", drivers=drivers)
        
        driver = Driver.query.get(driver_id_val)
        if not driver:
            flash("السائق غير موجود.", "danger")
            drivers = Driver.query.all()
            return render_template("add_salary.html", drivers=drivers)
        
        # تحويل التواريخ
        period_start_val = None
        period_end_val = None
        if period_start:
            try:
                period_start_val = datetime.strptime(period_start, "%Y-%m-%d").date()
            except ValueError:
                flash("تاريخ بداية الفترة غير صالح.", "danger")
                drivers = Driver.query.all()
                return render_template("add_salary.html", drivers=drivers)
        
        if period_end:
            try:
                period_end_val = datetime.strptime(period_end, "%Y-%m-%d").date()
            except ValueError:
                flash("تاريخ نهاية الفترة غير صالح.", "danger")
                drivers = Driver.query.all()
                return render_template("add_salary.html", drivers=drivers)
        
        salary = Salary(
            driver_id=driver_id_val,
            amount=amount_val,
            period_start=period_start_val,
            period_end=period_end_val
        )
        db.session.add(salary)
        db.session.commit()
        flash("تمت إضافة الراتب بنجاح.", "success")
        return redirect(url_for("salaries"))
    
    drivers = Driver.query.all()
    return render_template("add_salary.html", drivers=drivers)


@app.route("/salaries/delete/<int:salary_id>", methods=["POST"])
@login_required
def delete_salary(salary_id):
    salary = Salary.query.get(salary_id)
    if not salary:
        flash("الراتب غير موجود.", "warning")
        return redirect(url_for("salaries"))
    
    db.session.delete(salary)
    db.session.commit()
    flash("تم حذف الراتب.", "info")
    return redirect(url_for("salaries"))


# -------------------------------
# إدارة المصروفات
# -------------------------------
@app.route("/expenses")
@login_required
def expenses():
    expenses = Expense.query.order_by(Expense.occurred_at.desc()).all()
    return render_template("expenses.html", expenses=expenses)


@app.route("/expenses/add", methods=["GET", "POST"])
@login_required
def add_expense():
    if request.method == "POST":
        driver_id = request.form.get("driver_id", "").strip()
        vehicle_id = request.form.get("vehicle_id", "").strip()
        amount = request.form.get("amount", "").strip()
        category = request.form.get("category", "").strip()
        note = request.form.get("note", "").strip()
        
        if not amount:
            flash("يرجى إدخال المبلغ.", "danger")
            drivers = Driver.query.all()
            vehicles = Vehicle.query.all()
            return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)
        
        try:
            amount_val = float(amount)
        except ValueError:
            flash("يرجى إدخال مبلغ صالح.", "danger")
            drivers = Driver.query.all()
            vehicles = Vehicle.query.all()
            return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)
        
        driver_id_val = None
        if driver_id:
            try:
                driver_id_val = int(driver_id)
                if not Driver.query.get(driver_id_val):
                    flash("السائق المحدد غير موجود.", "danger")
                    drivers = Driver.query.all()
                    vehicles = Vehicle.query.all()
                    return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)
            except ValueError:
                flash("معرف السائق غير صالح.", "danger")
                drivers = Driver.query.all()
                vehicles = Vehicle.query.all()
                return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)
        
        vehicle_id_val = None
        if vehicle_id:
            try:
                vehicle_id_val = int(vehicle_id)
                if not Vehicle.query.get(vehicle_id_val):
                    flash("المركبة المحددة غير موجودة.", "danger")
                    drivers = Driver.query.all()
                    vehicles = Vehicle.query.all()
                    return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)
            except ValueError:
                flash("معرف المركبة غير صالح.", "danger")
                drivers = Driver.query.all()
                vehicles = Vehicle.query.all()
                return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)
        
        expense = Expense(
            driver_id=driver_id_val,
            vehicle_id=vehicle_id_val,
            amount=amount_val,
            category=category,
            note=note
        )
        db.session.add(expense)
        db.session.commit()
        flash("تمت إضافة المصروف بنجاح.", "success")
        return redirect(url_for("expenses"))
    
    drivers = Driver.query.all()
    vehicles = Vehicle.query.all()
    return render_template("add_expense.html", drivers=drivers, vehicles=vehicles)


@app.route("/expenses/delete/<int:expense_id>", methods=["POST"])
@login_required
def delete_expense(expense_id):
    expense = Expense.query.get(expense_id)
    if not expense:
        flash("المصروف غير موجود.", "warning")
        return redirect(url_for("expenses"))
    
    db.session.delete(expense)
    db.session.commit()
    flash("تم حذف المصروف.", "info")
    return redirect(url_for("expenses"))


# -------------------------------
# صفحات إضافية: تفاصيل، تقارير، إعدادات
# -------------------------------
@app.route("/drivers/<int:driver_id>")
@login_required
def driver_detail(driver_id):
    driver = Driver.query.get(driver_id)
    if not driver:
        flash("السائق غير موجود.", "warning")
        return redirect(url_for("dashboard"))
    
    # جلب آخر المدفوعات والرواتب والمصروفات
    recent_payments = Payment.query.filter_by(driver_id=driver_id).order_by(Payment.created_at.desc()).limit(5).all()
    recent_salaries = Salary.query.filter_by(driver_id=driver_id).order_by(Salary.paid_at.desc()).limit(5).all()
    recent_expenses = Expense.query.filter_by(driver_id=driver_id).order_by(Expense.occurred_at.desc()).limit(5).all()
    
    return render_template("driver_detail.html", 
                         driver=driver, 
                         recent_payments=recent_payments,
                         recent_salaries=recent_salaries,
                         recent_expenses=recent_expenses)


@app.route("/reports")
@login_required
def reports():
    # إحصائيات السائقين
    total_drivers = db.session.query(func.count(Driver.id)).scalar() or 0
    total_balance = db.session.query(func.coalesce(func.sum(Driver.balance), 0)).scalar() or 0
    avg_balance = db.session.query(func.coalesce(func.avg(Driver.balance), 0)).scalar() or 0
    
    # إحصائيات المركبات
    total_vehicles = db.session.query(func.count(Vehicle.id)).scalar() or 0
    assigned_vehicles = db.session.query(func.count(Vehicle.id)).filter(Vehicle.driver_id.isnot(None)).scalar() or 0
    unassigned_vehicles = total_vehicles - assigned_vehicles
    
    # إحصائيات المدفوعات
    total_payments_in = db.session.query(func.coalesce(func.sum(Payment.amount), 0)).filter(Payment.direction == 'in').scalar() or 0
    total_payments_out = db.session.query(func.coalesce(func.sum(Payment.amount), 0)).filter(Payment.direction == 'out').scalar() or 0
    total_payments_count = db.session.query(func.count(Payment.id)).scalar() or 0
    
    # إحصائيات الرواتب
    total_salaries = db.session.query(func.coalesce(func.sum(Salary.amount), 0)).scalar() or 0
    total_salaries_count = db.session.query(func.count(Salary.id)).scalar() or 0
    
    # إحصائيات المصروفات
    total_expenses = db.session.query(func.coalesce(func.sum(Expense.amount), 0)).scalar() or 0
    total_expenses_count = db.session.query(func.count(Expense.id)).scalar() or 0
    
    # أحدث العمليات
    latest_drivers = Driver.query.order_by(Driver.id.desc()).limit(5).all()
    latest_payments = Payment.query.order_by(Payment.created_at.desc()).limit(5).all()
    latest_expenses = Expense.query.order_by(Expense.occurred_at.desc()).limit(5).all()
    
    # إحصائيات المصروفات حسب الفئة
    expense_categories = db.session.query(
        Expense.category,
        func.sum(Expense.amount).label('total'),
        func.count(Expense.id).label('count')
    ).filter(Expense.category.isnot(None)).group_by(Expense.category).all()
    
    return render_template(
        "reports.html",
        # إحصائيات السائقين
        total_drivers=total_drivers,
        total_balance=total_balance,
        avg_balance=avg_balance,
        # إحصائيات المركبات
        total_vehicles=total_vehicles,
        assigned_vehicles=assigned_vehicles,
        unassigned_vehicles=unassigned_vehicles,
        # إحصائيات المدفوعات
        total_payments_in=total_payments_in,
        total_payments_out=total_payments_out,
        total_payments_count=total_payments_count,
        # إحصائيات الرواتب
        total_salaries=total_salaries,
        total_salaries_count=total_salaries_count,
        # إحصائيات المصروفات
        total_expenses=total_expenses,
        total_expenses_count=total_expenses_count,
        expense_categories=expense_categories,
        # أحدث العمليات
        latest_drivers=latest_drivers,
        latest_payments=latest_payments,
        latest_expenses=latest_expenses,
    )


@app.route("/settings", methods=["GET", "POST"])
@login_required
def settings():
    user = User.query.get(session["user_id"]) if "user_id" in session else None
    if request.method == "POST":
        current_password = request.form.get("current_password", "")
        new_password = request.form.get("new_password", "")
        confirm_password = request.form.get("confirm_password", "")
        if not user or not user.check_password(current_password):
            flash("كلمة المرور الحالية غير صحيحة.", "danger")
            return render_template("settings.html")
        if len(new_password) < 6:
            flash("يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل.", "warning")
            return render_template("settings.html")
        if new_password != confirm_password:
            flash("كلمتا المرور غير متطابقتين.", "warning")
            return render_template("settings.html")
        user.set_password(new_password)
        db.session.commit()
        flash("تم تحديث كلمة المرور بنجاح.", "success")
        return redirect(url_for("settings"))
    return render_template("settings.html")


# -------------------------------
# دوال التصدير
# -------------------------------

@app.route("/export/drivers")
@login_required
def export_drivers():
    """تصدير بيانات السائقين إلى Excel"""
    drivers = Driver.query.order_by(Driver.id).all()
    
    # إنشاء ملف Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "السائقون"
    
    # تنسيق العناوين
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # العناوين
    headers = ["الرقم", "الاسم", "رقم الهاتف", "الرصيد", "عدد المركبات", "تاريخ الإضافة"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # البيانات
    for row, driver in enumerate(drivers, 2):
        ws.cell(row=row, column=1, value=driver.id)
        ws.cell(row=row, column=2, value=driver.name)
        ws.cell(row=row, column=3, value=driver.phone)
        ws.cell(row=row, column=4, value=float(driver.balance))
        ws.cell(row=row, column=5, value=len(driver.vehicles))
        ws.cell(row=row, column=6, value=driver.created_at.strftime('%Y-%m-%d %H:%M'))
    
    # تعديل عرض الأعمدة
    ws.column_dimensions['A'].width = 8
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 15
    ws.column_dimensions['D'].width = 12
    ws.column_dimensions['E'].width = 12
    ws.column_dimensions['F'].width = 18
    
    # حفظ الملف في الذاكرة
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    # إرسال الملف
    return send_file(
        output,
        as_attachment=True,
        download_name=f"drivers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


@app.route("/export/payments")
@login_required
def export_payments():
    """تصدير بيانات المدفوعات إلى Excel"""
    payments = Payment.query.order_by(Payment.created_at.desc()).all()
    
    wb = Workbook()
    ws = wb.active
    ws.title = "المدفوعات"
    
    # تنسيق العناوين
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="28a745", end_color="28a745", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # العناوين
    headers = ["الرقم", "السائق", "المبلغ", "النوع", "الطريقة", "التاريخ", "ملاحظة"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # البيانات
    for row, payment in enumerate(payments, 2):
        ws.cell(row=row, column=1, value=payment.id)
        ws.cell(row=row, column=2, value=payment.driver.name)
        ws.cell(row=row, column=3, value=float(payment.amount))
        ws.cell(row=row, column=4, value="دخل" if payment.direction == 'in' else "خرج")
        ws.cell(row=row, column=5, value=payment.method or "")
        ws.cell(row=row, column=6, value=payment.created_at.strftime('%Y-%m-%d %H:%M'))
        ws.cell(row=row, column=7, value=payment.note or "")
    
    # تعديل عرض الأعمدة
    ws.column_dimensions['A'].width = 8
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 12
    ws.column_dimensions['D'].width = 10
    ws.column_dimensions['E'].width = 12
    ws.column_dimensions['F'].width = 18
    ws.column_dimensions['G'].width = 30
    
    # حفظ الملف في الذاكرة
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    return send_file(
        output,
        as_attachment=True,
        download_name=f"payments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


@app.route("/export/expenses")
@login_required
def export_expenses():
    """تصدير بيانات المصروفات إلى Excel"""
    expenses = Expense.query.order_by(Expense.occurred_at.desc()).all()
    
    wb = Workbook()
    ws = wb.active
    ws.title = "المصروفات"
    
    # تنسيق العناوين
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="dc3545", end_color="dc3545", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # العناوين
    headers = ["الرقم", "السائق", "المركبة", "المبلغ", "الفئة", "التاريخ", "ملاحظة"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # البيانات
    for row, expense in enumerate(expenses, 2):
        ws.cell(row=row, column=1, value=expense.id)
        ws.cell(row=row, column=2, value=expense.driver.name if expense.driver else "")
        ws.cell(row=row, column=3, value=expense.vehicle.plate_number if expense.vehicle else "")
        ws.cell(row=row, column=4, value=float(expense.amount))
        ws.cell(row=row, column=5, value=expense.category or "")
        ws.cell(row=row, column=6, value=expense.occurred_at.strftime('%Y-%m-%d %H:%M'))
        ws.cell(row=row, column=7, value=expense.note or "")
    
    # تعديل عرض الأعمدة
    ws.column_dimensions['A'].width = 8
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 15
    ws.column_dimensions['D'].width = 12
    ws.column_dimensions['E'].width = 12
    ws.column_dimensions['F'].width = 18
    ws.column_dimensions['G'].width = 30
    
    # حفظ الملف في الذاكرة
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    return send_file(
        output,
        as_attachment=True,
        download_name=f"expenses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


@app.route("/analytics")
@login_required
def analytics():
    """صفحة الإحصائيات المتقدمة مع الرسوم البيانية"""
    import json
    from datetime import datetime, timedelta
    from collections import defaultdict
    
    # إحصائيات المدفوعات الشهرية (آخر 6 أشهر)
    six_months_ago = datetime.now() - timedelta(days=180)
    monthly_payments = db.session.query(
        func.strftime('%Y-%m', Payment.created_at).label('month'),
        func.sum(Payment.amount).label('total'),
        Payment.direction
    ).filter(
        Payment.created_at >= six_months_ago
    ).group_by('month', Payment.direction).all()
    
    # تنظيم بيانات المدفوعات الشهرية
    monthly_data = defaultdict(lambda: {'in': 0, 'out': 0})
    for payment in monthly_payments:
        monthly_data[payment.month][payment.direction] = float(payment.total)
    
    # إعداد بيانات الرسم البياني للمدفوعات
    months = sorted(monthly_data.keys())
    monthly_payments_data = {
        'labels': months,
        'income': [monthly_data[month]['in'] for month in months],
        'outcome': [monthly_data[month]['out'] for month in months]
    }
    
    # إحصائيات المصروفات الشهرية
    monthly_expenses = db.session.query(
        func.strftime('%Y-%m', Expense.occurred_at).label('month'),
        func.sum(Expense.amount).label('total')
    ).filter(
        Expense.occurred_at >= six_months_ago
    ).group_by('month').all()
    
    monthly_expenses_data = {
        'labels': [exp.month for exp in monthly_expenses],
        'amounts': [float(exp.total) for exp in monthly_expenses]
    }
    
    # توزيع الأرصدة
    positive_balance = Driver.query.filter(Driver.balance > 0).count()
    negative_balance = Driver.query.filter(Driver.balance < 0).count()
    zero_balance = Driver.query.filter(Driver.balance == 0).count()
    
    balance_distribution_data = {
        'labels': ['رصيد موجب', 'رصيد سالب', 'رصيد صفر'],
        'values': [positive_balance, negative_balance, zero_balance]
    }
    
    # المصروفات حسب الفئة
    expense_categories = db.session.query(
        Expense.category,
        func.sum(Expense.amount).label('total')
    ).filter(
        Expense.category.isnot(None)
    ).group_by(Expense.category).all()
    
    expense_categories_data = {
        'labels': [cat.category for cat in expense_categories],
        'values': [float(cat.total) for cat in expense_categories]
    }
    
    # أعلى السائقين برصيد موجب
    top_positive_drivers = Driver.query.filter(Driver.balance > 0).order_by(Driver.balance.desc()).limit(5).all()
    
    # أعلى السائقين برصيد سالب
    top_negative_drivers = Driver.query.filter(Driver.balance < 0).order_by(Driver.balance.asc()).limit(5).all()
    
    # أكثر السائقين نشاطاً (عدد المدفوعات)
    most_active_drivers = db.session.query(
        Driver,
        func.count(Payment.id).label('payment_count')
    ).join(Payment).group_by(Driver.id).order_by(func.count(Payment.id).desc()).limit(5).all()
    
    # تحويل البيانات إلى JSON للاستخدام في JavaScript
    return render_template('analytics.html',
                         monthly_payments_data=json.dumps(monthly_payments_data),
                         monthly_expenses_data=json.dumps(monthly_expenses_data),
                         balance_distribution_data=json.dumps(balance_distribution_data),
                         expense_categories_data=json.dumps(expense_categories_data),
                         top_positive_drivers=top_positive_drivers,
                         top_negative_drivers=top_negative_drivers,
                         most_active_drivers=most_active_drivers)


@app.route("/backup")
@login_required
def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    import shutil
    
    try:
        # إنشاء نسخة من ملف قاعدة البيانات
        backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(BASE_DIR, backup_filename)
        
        shutil.copy2(DATABASE_PATH, backup_path)
        
        # إرسال الملف للتحميل
        return send_file(
            backup_path,
            as_attachment=True,
            download_name=backup_filename,
            mimetype='application/octet-stream'
        )
    except Exception as e:
        flash(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", "danger")
        return redirect(url_for("settings"))


@app.route("/data-integrity")
@login_required
def data_integrity():
    """فحص وإصلاح تكامل البيانات"""
    issues = []
    fixes_applied = 0
    
    # فحص أرصدة السائقين
    drivers = Driver.query.all()
    for driver in drivers:
        # حساب الرصيد الفعلي من المدفوعات
        payments_in = db.session.query(func.sum(Payment.amount)).filter_by(driver_id=driver.id, direction='in').scalar() or 0
        payments_out = db.session.query(func.sum(Payment.amount)).filter_by(driver_id=driver.id, direction='out').scalar() or 0
        calculated_balance = payments_in - payments_out
        
        if abs(driver.balance - calculated_balance) > 0.01:  # تسامح صغير للأرقام العشرية
            issues.append({
                'type': 'balance_mismatch',
                'driver': driver.name,
                'current_balance': driver.balance,
                'calculated_balance': calculated_balance,
                'difference': driver.balance - calculated_balance
            })
            
            # إصلاح الرصيد
            if request.args.get('fix') == 'true':
                driver.balance = calculated_balance
                fixes_applied += 1
    
    # فحص المركبات بدون سائق
    orphaned_vehicles = Vehicle.query.filter_by(driver_id=None).all()
    if orphaned_vehicles:
        issues.append({
            'type': 'orphaned_vehicles',
            'count': len(orphaned_vehicles),
            'vehicles': [v.plate_number for v in orphaned_vehicles]
        })
    
    # فحص المدفوعات بدون سائق
    orphaned_payments = Payment.query.filter(~Payment.driver_id.in_(
        db.session.query(Driver.id)
    )).all()
    if orphaned_payments:
        issues.append({
            'type': 'orphaned_payments',
            'count': len(orphaned_payments),
            'payment_ids': [p.id for p in orphaned_payments]
        })
    
    # فحص المصروفات بدون سائق أو مركبة
    orphaned_expenses = Expense.query.filter(
        db.and_(
            Expense.driver_id.is_(None),
            Expense.vehicle_id.is_(None)
        )
    ).all()
    if orphaned_expenses:
        issues.append({
            'type': 'orphaned_expenses',
            'count': len(orphaned_expenses),
            'expense_ids': [e.id for e in orphaned_expenses]
        })
    
    if request.args.get('fix') == 'true' and fixes_applied > 0:
        db.session.commit()
        flash(f"تم إصلاح {fixes_applied} مشكلة في البيانات.", "success")
        return redirect(url_for('data_integrity'))
    
    return render_template('data_integrity.html', issues=issues)


@app.route("/seed-data", methods=["POST"])
@login_required
def seed_data():
    """إنشاء بيانات تجريبية للاختبار"""
    import random
    from datetime import datetime, timedelta
    
    try:
        # إنشاء سائقين تجريبيين
        sample_drivers = [
            {"name": "أحمد محمد", "phone": "0501234567"},
            {"name": "محمد علي", "phone": "0509876543"},
            {"name": "علي حسن", "phone": "0555555555"},
            {"name": "حسن أحمد", "phone": "0544444444"},
            {"name": "عبدالله سعد", "phone": "0533333333"},
        ]
        
        created_drivers = []
        for driver_data in sample_drivers:
            if not Driver.query.filter_by(phone=driver_data["phone"]).first():
                driver = Driver(name=driver_data["name"], phone=driver_data["phone"])
                db.session.add(driver)
                created_drivers.append(driver)
        
        db.session.commit()
        
        # إنشاء مركبات تجريبية
        sample_vehicles = [
            {"plate_number": "أ ب ج 123", "model": "تويوتا كامري", "year": 2020},
            {"plate_number": "د هـ و 456", "model": "نيسان التيما", "year": 2019},
            {"plate_number": "ز ح ط 789", "model": "هيونداي إلنترا", "year": 2021},
            {"plate_number": "ي ك ل 321", "model": "كيا سيراتو", "year": 2018},
            {"plate_number": "م ن س 654", "model": "تويوتا كورولا", "year": 2022},
        ]
        
        for i, vehicle_data in enumerate(sample_vehicles):
            if not Vehicle.query.filter_by(plate_number=vehicle_data["plate_number"]).first():
                driver = created_drivers[i] if i < len(created_drivers) else None
                vehicle = Vehicle(
                    plate_number=vehicle_data["plate_number"],
                    model=vehicle_data["model"],
                    year=vehicle_data["year"],
                    driver_id=driver.id if driver else None
                )
                db.session.add(vehicle)
        
        db.session.commit()
        
        # إنشاء مدفوعات تجريبية
        for driver in created_drivers:
            for _ in range(random.randint(3, 8)):
                payment = Payment(
                    driver_id=driver.id,
                    amount=random.uniform(50, 500),
                    direction=random.choice(['in', 'out']),
                    method=random.choice(['نقد', 'تحويل', 'شيك']),
                    note=f"مدفوعة تجريبية للسائق {driver.name}",
                    created_at=datetime.now() - timedelta(days=random.randint(1, 90))
                )
                db.session.add(payment)
                
                # تحديث رصيد السائق
                if payment.direction == 'in':
                    driver.balance += payment.amount
                else:
                    driver.balance -= payment.amount
        
        # إنشاء مصروفات تجريبية
        categories = ['وقود', 'صيانة', 'تأمين', 'رسوم', 'أخرى']
        vehicles = Vehicle.query.all()
        
        for _ in range(20):
            vehicle = random.choice(vehicles) if vehicles else None
            expense = Expense(
                driver_id=vehicle.driver_id if vehicle else random.choice(created_drivers).id,
                vehicle_id=vehicle.id if vehicle else None,
                amount=random.uniform(20, 200),
                category=random.choice(categories),
                note="مصروف تجريبي",
                occurred_at=datetime.now() - timedelta(days=random.randint(1, 60))
            )
            db.session.add(expense)
        
        # إنشاء رواتب تجريبية
        for driver in created_drivers:
            for month_offset in range(1, 4):  # آخر 3 أشهر
                salary_date = datetime.now() - timedelta(days=30 * month_offset)
                salary = Salary(
                    driver_id=driver.id,
                    amount=random.uniform(1000, 3000),
                    month=salary_date.strftime('%Y-%m'),
                    note=f"راتب شهر {salary_date.strftime('%Y-%m')}",
                    paid_at=salary_date
                )
                db.session.add(salary)
        
        db.session.commit()
        flash("تم إنشاء البيانات التجريبية بنجاح!", "success")
        
    except Exception as e:
        db.session.rollback()
        flash(f"خطأ في إنشاء البيانات التجريبية: {str(e)}", "danger")
    
    return redirect(url_for('settings'))


@app.route("/print-report")
@login_required
def print_report():
    """إنشاء تقرير قابل للطباعة"""
    from datetime import datetime
    
    report_type = request.args.get('type', 'drivers')
    
    # إعداد البيانات حسب نوع التقرير
    data = {
        'report_title': '',
        'current_date': datetime.now().strftime('%Y-%m-%d'),
        'current_time': datetime.now().strftime('%H:%M:%S'),
        'summary': {},
        'drivers': [],
        'payments': [],
        'expenses': [],
        'salaries': []
    }
    
    if report_type == 'drivers':
        data['report_title'] = 'تقرير السائقين الشامل'
        data['drivers'] = Driver.query.order_by(Driver.name).all()
        data['summary'] = {
            'إجمالي السائقين': len(data['drivers']),
            'أرصدة موجبة': len([d for d in data['drivers'] if d.balance > 0]),
            'أرصدة سالبة': len([d for d in data['drivers'] if d.balance < 0]),
            'إجمالي الأرصدة': f"{sum(d.balance for d in data['drivers']):.2f}"
        }
    
    elif report_type == 'payments':
        data['report_title'] = 'تقرير المدفوعات'
        data['payments'] = Payment.query.order_by(Payment.created_at.desc()).limit(100).all()
        total_in = sum(p.amount for p in data['payments'] if p.direction == 'in')
        total_out = sum(p.amount for p in data['payments'] if p.direction == 'out')
        data['summary'] = {
            'إجمالي المدفوعات': len(data['payments']),
            'إجمالي الداخل': f"{total_in:.2f}",
            'إجمالي الخارج': f"{total_out:.2f}",
            'الصافي': f"{total_in - total_out:.2f}"
        }
    
    elif report_type == 'expenses':
        data['report_title'] = 'تقرير المصروفات'
        data['expenses'] = Expense.query.order_by(Expense.occurred_at.desc()).limit(100).all()
        data['summary'] = {
            'إجمالي المصروفات': len(data['expenses']),
            'إجمالي المبلغ': f"{sum(e.amount for e in data['expenses']):.2f}"
        }
    
    elif report_type == 'salaries':
        data['report_title'] = 'تقرير الرواتب'
        data['salaries'] = Salary.query.order_by(Salary.paid_at.desc()).limit(100).all()
        data['summary'] = {
            'إجمالي الرواتب': len(data['salaries']),
            'إجمالي المبلغ': f"{sum(s.amount for s in data['salaries']):.2f}"
        }
    
    return render_template('print_report.html', **data)


@app.route("/api/stats")
@login_required
def api_stats():
    """API للحصول على الإحصائيات المحدثة"""
    drivers = Driver.query.all()
    
    stats = {
        'drivers_count': len(drivers),
        'positive_balance_count': len([d for d in drivers if d.balance > 0]),
        'negative_balance_count': len([d for d in drivers if d.balance < 0]),
        'zero_balance_count': len([d for d in drivers if d.balance == 0]),
        'total_balance': sum(d.balance for d in drivers),
        'vehicles_count': Vehicle.query.count(),
        'payments_count': Payment.query.count(),
        'expenses_count': Expense.query.count(),
        'salaries_count': Salary.query.count(),
        'last_payment': Payment.query.order_by(Payment.created_at.desc()).first(),
        'last_expense': Expense.query.order_by(Expense.occurred_at.desc()).first()
    }
    
    # تحويل التواريخ إلى نص
    if stats['last_payment']:
        stats['last_payment'] = {
            'id': stats['last_payment'].id,
            'driver_name': stats['last_payment'].driver.name,
            'amount': stats['last_payment'].amount,
            'direction': stats['last_payment'].direction,
            'created_at': stats['last_payment'].created_at.strftime('%Y-%m-%d %H:%M')
        }
    
    if stats['last_expense']:
        stats['last_expense'] = {
            'id': stats['last_expense'].id,
            'amount': stats['last_expense'].amount,
            'category': stats['last_expense'].category,
            'occurred_at': stats['last_expense'].occurred_at.strftime('%Y-%m-%d')
        }
    
    return jsonify(stats)


@app.route("/health")
def health_check():
    """فحص صحة التطبيق"""
    try:
        # فحص قاعدة البيانات
        db.session.execute(text("SELECT 1"))
        
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.now().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route("/backup")
@login_required
def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        from datetime import datetime
        import shutil
        import os
        
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = os.path.join(os.path.dirname(__file__), 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # نسخ قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'drivers.db')
        shutil.copy2(db_path, backup_path)
        
        flash(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}", "success")
        
        return jsonify({
            'success': True,
            'filename': backup_filename,
            'path': backup_path,
            'size': os.path.getsize(backup_path)
        })
        
    except Exception as e:
        flash(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", "danger")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route("/restore", methods=["POST"])
@login_required
def restore_backup():
    """استعادة نسخة احتياطية"""
    try:
        if 'backup_file' not in request.files:
            flash("لم يتم اختيار ملف النسخة الاحتياطية", "danger")
            return redirect(url_for('settings'))
        
        file = request.files['backup_file']
        if file.filename == '':
            flash("لم يتم اختيار ملف", "danger")
            return redirect(url_for('settings'))
        
        if file and file.filename.endswith('.db'):
            import os
            import shutil
            
            # حفظ النسخة الحالية كنسخة احتياطية
            current_db = os.path.join(os.path.dirname(__file__), 'drivers.db')
            backup_current = os.path.join(os.path.dirname(__file__), 'backups', f'before_restore_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db')
            shutil.copy2(current_db, backup_current)
            
            # استعادة النسخة الاحتياطية
            file.save(current_db)
            
            flash("تم استعادة النسخة الاحتياطية بنجاح", "success")
        else:
            flash("نوع الملف غير صحيح. يجب أن يكون ملف .db", "danger")
            
    except Exception as e:
        flash(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "danger")
    
    return redirect(url_for('settings'))


@app.route("/api/notifications")
@login_required
def get_notifications():
    """الحصول على الإشعارات"""
    if not notification_manager:
        return jsonify([])
    
    user_id = session.get('user_id')
    unread_only = request.args.get('unread_only', 'false').lower() == 'true'
    
    notifications = notification_manager.get_notifications(user_id=user_id, unread_only=unread_only)
    
    return jsonify(notifications)


@app.route("/api/notifications/<int:notification_id>/read", methods=["POST"])
@login_required
def mark_notification_read(notification_id):
    """تمييز الإشعار كمقروء"""
    if not notification_manager:
        return jsonify({'success': False, 'error': 'نظام الإشعارات غير متاح'})
    
    user_id = session.get('user_id')
    success = notification_manager.mark_as_read(notification_id, user_id)
    
    return jsonify({'success': success})


@app.route("/notifications")
@login_required
def notifications_page():
    """صفحة الإشعارات"""
    if not notification_manager:
        flash("نظام الإشعارات غير متاح", "warning")
        return redirect(url_for('dashboard'))
    
    user_id = session.get('user_id')
    notifications = notification_manager.get_notifications(user_id=user_id)
    
    return render_template('notifications.html', notifications=notifications)


@app.route("/api/system/status")
@login_required
def system_status():
    """الحصول على حالة النظام"""
    try:
        from monitoring import system_monitor
        status = system_monitor.get_system_status()
        return jsonify(status or {'status': 'unknown'})
    except ImportError:
        return jsonify({'status': 'monitoring_unavailable'})


@app.route("/api/system/metrics")
@login_required
def system_metrics():
    """الحصول على مقاييس النظام"""
    try:
        from monitoring import system_monitor
        hours = request.args.get('hours', 24, type=int)
        metrics = system_monitor.get_metrics_history(hours)
        return jsonify(metrics)
    except ImportError:
        return jsonify([])


@app.route("/api/system/alerts")
@login_required
def system_alerts():
    """الحصول على تنبيهات النظام"""
    try:
        from monitoring import system_monitor
        hours = request.args.get('hours', 24, type=int)
        alerts = system_monitor.get_alerts_history(hours)
        return jsonify(alerts)
    except ImportError:
        return jsonify([])


@app.route("/monitoring")
@login_required
def monitoring_dashboard():
    """لوحة مراقبة النظام"""
    try:
        from monitoring import system_monitor
        
        status = system_monitor.get_system_status()
        report = system_monitor.generate_report()
        
        return render_template('monitoring.html', 
                             status=status, 
                             report=report)
    except ImportError:
        flash("نظام المراقبة غير متاح", "warning")
        return redirect(url_for('dashboard'))


@app.route("/backup")
@login_required
def backup_page():
    """صفحة إدارة النسخ الاحتياطية"""
    try:
        from backup_manager import backup_manager
        backups = backup_manager.list_backups()
        config = backup_manager.config
        
        return render_template('backup.html', backups=backups, config=config)
    except ImportError:
        flash("نظام النسخ الاحتياطية غير متاح", "warning")
        return redirect(url_for('dashboard'))


@app.route("/api/backup/create", methods=["POST"])
@login_required
def api_create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        from backup_manager import backup_manager
        
        data = request.get_json() or {}
        description = data.get('description', 'نسخة احتياطية يدوية')
        
        result = backup_manager.create_backup('manual', description)
        
        return jsonify(result)
    except ImportError:
        return jsonify({'success': False, 'error': 'نظام النسخ الاحتياطية غير متاح'})


@app.route("/api/backup/list")
@login_required
def api_list_backups():
    """قائمة النسخ الاحتياطية"""
    try:
        from backup_manager import backup_manager
        backups = backup_manager.list_backups()
        
        return jsonify({'success': True, 'backups': backups})
    except ImportError:
        return jsonify({'success': False, 'error': 'نظام النسخ الاحتياطية غير متاح'})


@app.route("/api/backup/restore", methods=["POST"])
@login_required
def api_restore_backup():
    """استعادة نسخة احتياطية"""
    try:
        from backup_manager import backup_manager
        
        data = request.get_json()
        backup_file = data.get('backup_file')
        
        if not backup_file:
            return jsonify({'success': False, 'error': 'ملف النسخة الاحتياطية مطلوب'})
        
        result = backup_manager.restore_backup(backup_file)
        
        return jsonify(result)
    except ImportError:
        return jsonify({'success': False, 'error': 'نظام النسخ الاحتياطية غير متاح'})


@app.route("/api/backup/verify", methods=["POST"])
@login_required
def api_verify_backup():
    """التحقق من سلامة النسخة الاحتياطية"""
    try:
        from backup_manager import backup_manager
        
        data = request.get_json()
        backup_file = data.get('backup_file')
        
        if not backup_file:
            return jsonify({'success': False, 'error': 'ملف النسخة الاحتياطية مطلوب'})
        
        result = backup_manager.verify_backup(backup_file)
        
        return jsonify({'success': True, 'verification': result})
    except ImportError:
        return jsonify({'success': False, 'error': 'نظام النسخ الاحتياطية غير متاح'})


@app.route("/files")
@login_required
def file_manager_page():
    """صفحة إدارة الملفات"""
    try:
        return render_template('file_manager.html')
    except Exception as e:
        flash("حدث خطأ في تحميل صفحة إدارة الملفات", "error")
        return redirect(url_for('dashboard'))


@app.route("/api/files/upload", methods=["POST"])
@login_required
def api_upload_files():
    """رفع الملفات"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        files = request.files.getlist('files')
        description = request.form.get('description')
        
        if not files:
            return jsonify({'success': False, 'error': 'لم يتم اختيار ملفات'})
        
        uploaded_files = []
        errors = []
        
        for file in files:
            result = file_manager.upload_file(file, description=description)
            if result['success']:
                uploaded_files.append(result['file_info'])
            else:
                errors.append(f"{file.filename}: {result['error']}")
        
        if uploaded_files:
            return jsonify({
                'success': True,
                'message': f'تم رفع {len(uploaded_files)} ملف بنجاح',
                'files': uploaded_files,
                'errors': errors
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في رفع جميع الملفات',
                'errors': errors
            })
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/list")
@login_required
def api_list_files():
    """قائمة الملفات"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        page = request.args.get('page', 1, type=int)
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        
        result = file_manager.list_files(
            category=category if category else None,
            page=page
        )
        
        # فلترة البحث
        if search:
            filtered_files = []
            for file in result['files']:
                if search.lower() in file['original_name'].lower():
                    filtered_files.append(file)
            result['files'] = filtered_files
            result['total'] = len(filtered_files)
        
        return jsonify({
            'success': True,
            'files': result['files'],
            'pagination': {
                'page': result['current_page'],
                'pages': result['pages'],
                'total': result['total'],
                'has_prev': result['current_page'] > 1,
                'has_next': result['current_page'] < result['pages']
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/info/<filename>")
@login_required
def api_file_info(filename):
    """معلومات الملف"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        file_info = file_manager.get_file_metadata(filename)
        if not file_info:
            return jsonify({'success': False, 'error': 'الملف غير موجود'})
        
        return jsonify({'success': True, 'file': file_info})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/delete/<filename>", methods=["DELETE"])
@login_required
def api_delete_file(filename):
    """حذف ملف"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        result = file_manager.delete_file(filename)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/delete-multiple", methods=["DELETE"])
@login_required
def api_delete_multiple_files():
    """حذف ملفات متعددة"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        data = request.get_json()
        filenames = data.get('filenames', [])
        
        if not filenames:
            return jsonify({'success': False, 'error': 'لم يتم تحديد ملفات'})
        
        deleted_count = 0
        errors = []
        
        for filename in filenames:
            result = file_manager.delete_file(filename)
            if result['success']:
                deleted_count += 1
            else:
                errors.append(f"{filename}: {result['error']}")
        
        return jsonify({
            'success': True,
            'message': f'تم حذف {deleted_count} ملف',
            'deleted_count': deleted_count,
            'errors': errors
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/stats")
@login_required
def api_file_stats():
    """إحصائيات الملفات"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        stats = file_manager.get_storage_stats()
        return jsonify({'success': True, 'stats': stats})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/create-archive", methods=["POST"])
@login_required
def api_create_archive():
    """إنشاء أرشيف"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        data = request.get_json()
        filenames = data.get('filenames', [])
        archive_name = data.get('archive_name')
        
        result = file_manager.create_archive(filenames, archive_name)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@app.route("/api/files/cleanup", methods=["POST"])
@login_required
def api_cleanup_files():
    """تنظيف الملفات القديمة"""
    try:
        if not file_manager:
            return jsonify({'success': False, 'error': 'نظام إدارة الملفات غير متاح'})
        
        data = request.get_json()
        days = data.get('days', 30)
        
        result = file_manager.cleanup_old_files(days)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


# -------------------------------
# تهيئة قاعدة البيانات + إنشاء بيانات افتراضية
# -------------------------------

def init_db_and_seed():
    # ترحيل مبسط لإضافة role_id إذا لم يوجد
    migrate_sqlite_schema()
    # إنشاء الجداول الجديدة إن لم تكن موجودة
    db.create_all()

    # إنشاء دور ومستخدم افتراضيين
    admin_role = Role.query.filter_by(name="admin").first()
    if not admin_role:
        admin_role = Role(name="admin")
        db.session.add(admin_role)
        db.session.commit()

    admin_user = User.query.filter_by(username="admin").first()
    if not admin_user:
        admin_user = User(username="admin", role=admin_role)
        admin_user.set_password("admin123")
        db.session.add(admin_user)
        db.session.commit()
        print("Created default user: admin / admin123 (please change it!)")


# -------------------------------
# نقطة التشغيل
# -------------------------------
if __name__ == "__main__":
    if not os.path.exists(DATABASE_PATH):
        print("Initializing database...")
    with app.app_context():
        init_db_and_seed()
    app.run(host="127.0.0.1", port=5000, debug=True)