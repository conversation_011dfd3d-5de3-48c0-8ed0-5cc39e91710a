#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API متكامل لنظام إدارة السائقين
Complete API for Driver Management System

يوفر:
- REST API endpoints
- Authentication
- Data validation
- Error handling
- Rate limiting
- API documentation
"""

from flask import Blueprint, request, jsonify, g
from functools import wraps
import jwt
import datetime
from werkzeug.security import check_password_hash
from app import db, Driver, Vehicle, Payment, Expense, User
import logging

# إعداد السجلات
logger = logging.getLogger(__name__)

# إنشاء Blueprint للـ API
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

# مفتاح JWT (يجب أن يكون نفس مفتاح التطبيق)
JWT_SECRET = "CHANGE_THIS_SECRET_KEY"
JWT_ALGORITHM = "HS256"

class APIError(Exception):
    """استثناء مخصص للـ API"""
    def __init__(self, message, status_code=400, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload

def handle_api_error(error):
    """معالج أخطاء الـ API"""
    response = {
        'error': True,
        'message': error.message,
        'status_code': error.status_code
    }
    if error.payload:
        response['details'] = error.payload
    
    return jsonify(response), error.status_code

# تسجيل معالج الأخطاء
api_bp.errorhandler(APIError)(handle_api_error)

def generate_token(user_id):
    """إنشاء JWT token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24),
        'iat': datetime.datetime.utcnow()
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_token(token):
    """التحقق من JWT token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload['user_id']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(f):
    """decorator للمصادقة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        
        # البحث عن التوكن في الهيدر
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer TOKEN
            except IndexError:
                raise APIError('Invalid authorization header format', 401)
        
        if not token:
            raise APIError('Token is missing', 401)
        
        user_id = verify_token(token)
        if user_id is None:
            raise APIError('Token is invalid or expired', 401)
        
        # التحقق من وجود المستخدم
        user = User.query.get(user_id)
        if not user:
            raise APIError('User not found', 401)
        
        g.current_user = user
        return f(*args, **kwargs)
    
    return decorated_function

def validate_json(*required_fields):
    """decorator للتحقق من صحة JSON"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                raise APIError('Content-Type must be application/json', 400)
            
            data = request.get_json()
            if not data:
                raise APIError('No JSON data provided', 400)
            
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                raise APIError(f'Missing required fields: {", ".join(missing_fields)}', 400)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# ================================
# Authentication Endpoints
# ================================

@api_bp.route('/auth/login', methods=['POST'])
@validate_json('username', 'password')
def api_login():
    """تسجيل الدخول عبر API"""
    data = request.get_json()
    
    user = User.query.filter_by(username=data['username']).first()
    if not user or not check_password_hash(user.password, data['password']):
        raise APIError('Invalid username or password', 401)
    
    token = generate_token(user.id)
    
    return jsonify({
        'success': True,
        'token': token,
        'user': {
            'id': user.id,
            'username': user.username,
            'role': user.role.name if user.role else 'user'
        }
    })

@api_bp.route('/auth/verify', methods=['GET'])
@require_auth
def api_verify_token():
    """التحقق من صحة التوكن"""
    return jsonify({
        'success': True,
        'user': {
            'id': g.current_user.id,
            'username': g.current_user.username,
            'role': g.current_user.role.name if g.current_user.role else 'user'
        }
    })

# ================================
# Drivers Endpoints
# ================================

@api_bp.route('/drivers', methods=['GET'])
@require_auth
def api_get_drivers():
    """الحصول على قائمة السائقين"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')
    
    query = Driver.query
    
    if search:
        query = query.filter(Driver.name.contains(search))
    
    drivers = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'success': True,
        'data': [{
            'id': driver.id,
            'name': driver.name,
            'phone': driver.phone,
            'balance': float(driver.balance),
            'vehicle_id': driver.vehicle_id,
            'vehicle_plate': driver.vehicle.plate_number if driver.vehicle else None,
            'created_at': driver.created_at.isoformat() if driver.created_at else None
        } for driver in drivers.items],
        'pagination': {
            'page': drivers.page,
            'pages': drivers.pages,
            'per_page': drivers.per_page,
            'total': drivers.total,
            'has_next': drivers.has_next,
            'has_prev': drivers.has_prev
        }
    })

@api_bp.route('/drivers/<int:driver_id>', methods=['GET'])
@require_auth
def api_get_driver(driver_id):
    """الحصول على بيانات سائق محدد"""
    driver = Driver.query.get_or_404(driver_id)
    
    return jsonify({
        'success': True,
        'data': {
            'id': driver.id,
            'name': driver.name,
            'phone': driver.phone,
            'balance': float(driver.balance),
            'vehicle_id': driver.vehicle_id,
            'vehicle': {
                'id': driver.vehicle.id,
                'plate_number': driver.vehicle.plate_number,
                'model': driver.vehicle.model,
                'year': driver.vehicle.year
            } if driver.vehicle else None,
            'created_at': driver.created_at.isoformat() if driver.created_at else None,
            'payments_count': len(driver.payments),
            'total_payments': sum(p.amount for p in driver.payments)
        }
    })

@api_bp.route('/drivers', methods=['POST'])
@require_auth
@validate_json('name', 'phone')
def api_create_driver():
    """إنشاء سائق جديد"""
    data = request.get_json()
    
    # التحقق من عدم تكرار رقم الهاتف
    existing_driver = Driver.query.filter_by(phone=data['phone']).first()
    if existing_driver:
        raise APIError('Phone number already exists', 400)
    
    driver = Driver(
        name=data['name'],
        phone=data['phone'],
        balance=data.get('balance', 0.0),
        vehicle_id=data.get('vehicle_id')
    )
    
    db.session.add(driver)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Driver created successfully',
        'data': {
            'id': driver.id,
            'name': driver.name,
            'phone': driver.phone,
            'balance': float(driver.balance)
        }
    }), 201

@api_bp.route('/drivers/<int:driver_id>', methods=['PUT'])
@require_auth
@validate_json('name', 'phone')
def api_update_driver(driver_id):
    """تحديث بيانات سائق"""
    driver = Driver.query.get_or_404(driver_id)
    data = request.get_json()
    
    # التحقق من عدم تكرار رقم الهاتف
    existing_driver = Driver.query.filter(
        Driver.phone == data['phone'],
        Driver.id != driver_id
    ).first()
    if existing_driver:
        raise APIError('Phone number already exists', 400)
    
    driver.name = data['name']
    driver.phone = data['phone']
    if 'balance' in data:
        driver.balance = data['balance']
    if 'vehicle_id' in data:
        driver.vehicle_id = data['vehicle_id']
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Driver updated successfully',
        'data': {
            'id': driver.id,
            'name': driver.name,
            'phone': driver.phone,
            'balance': float(driver.balance)
        }
    })

@api_bp.route('/drivers/<int:driver_id>', methods=['DELETE'])
@require_auth
def api_delete_driver(driver_id):
    """حذف سائق"""
    driver = Driver.query.get_or_404(driver_id)
    
    # التحقق من عدم وجود مدفوعات مرتبطة
    if driver.payments:
        raise APIError('Cannot delete driver with existing payments', 400)
    
    db.session.delete(driver)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Driver deleted successfully'
    })

# ================================
# Vehicles Endpoints
# ================================

@api_bp.route('/vehicles', methods=['GET'])
@require_auth
def api_get_vehicles():
    """الحصول على قائمة المركبات"""
    vehicles = Vehicle.query.all()
    
    return jsonify({
        'success': True,
        'data': [{
            'id': vehicle.id,
            'plate_number': vehicle.plate_number,
            'model': vehicle.model,
            'year': vehicle.year,
            'driver_id': vehicle.driver.id if vehicle.driver else None,
            'driver_name': vehicle.driver.name if vehicle.driver else None
        } for vehicle in vehicles]
    })

@api_bp.route('/vehicles', methods=['POST'])
@require_auth
@validate_json('plate_number', 'model', 'year')
def api_create_vehicle():
    """إنشاء مركبة جديدة"""
    data = request.get_json()
    
    # التحقق من عدم تكرار رقم اللوحة
    existing_vehicle = Vehicle.query.filter_by(plate_number=data['plate_number']).first()
    if existing_vehicle:
        raise APIError('Plate number already exists', 400)
    
    vehicle = Vehicle(
        plate_number=data['plate_number'],
        model=data['model'],
        year=data['year']
    )
    
    db.session.add(vehicle)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Vehicle created successfully',
        'data': {
            'id': vehicle.id,
            'plate_number': vehicle.plate_number,
            'model': vehicle.model,
            'year': vehicle.year
        }
    }), 201

# ================================
# Payments Endpoints
# ================================

@api_bp.route('/payments', methods=['GET'])
@require_auth
def api_get_payments():
    """الحصول على قائمة المدفوعات"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    driver_id = request.args.get('driver_id', type=int)
    
    query = Payment.query
    
    if driver_id:
        query = query.filter_by(driver_id=driver_id)
    
    payments = query.order_by(Payment.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'success': True,
        'data': [{
            'id': payment.id,
            'amount': float(payment.amount),
            'description': payment.description,
            'driver_id': payment.driver_id,
            'driver_name': payment.driver.name,
            'created_at': payment.created_at.isoformat() if payment.created_at else None
        } for payment in payments.items],
        'pagination': {
            'page': payments.page,
            'pages': payments.pages,
            'per_page': payments.per_page,
            'total': payments.total
        }
    })

@api_bp.route('/payments', methods=['POST'])
@require_auth
@validate_json('driver_id', 'amount')
def api_create_payment():
    """إنشاء مدفوعة جديدة"""
    data = request.get_json()
    
    # التحقق من وجود السائق
    driver = Driver.query.get(data['driver_id'])
    if not driver:
        raise APIError('Driver not found', 404)
    
    payment = Payment(
        driver_id=data['driver_id'],
        amount=data['amount'],
        description=data.get('description', '')
    )
    
    # تحديث رصيد السائق
    driver.balance += payment.amount
    
    db.session.add(payment)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Payment created successfully',
        'data': {
            'id': payment.id,
            'amount': float(payment.amount),
            'driver_balance': float(driver.balance)
        }
    }), 201

# ================================
# Statistics Endpoints
# ================================

@api_bp.route('/stats/summary', methods=['GET'])
@require_auth
def api_get_stats_summary():
    """الحصول على ملخص الإحصائيات"""
    from sqlalchemy import func
    
    stats = {
        'drivers_count': Driver.query.count(),
        'vehicles_count': Vehicle.query.count(),
        'payments_count': Payment.query.count(),
        'expenses_count': Expense.query.count(),
        'total_payments': db.session.query(func.sum(Payment.amount)).scalar() or 0,
        'total_expenses': db.session.query(func.sum(Expense.amount)).scalar() or 0,
        'positive_balance_drivers': Driver.query.filter(Driver.balance > 0).count(),
        'negative_balance_drivers': Driver.query.filter(Driver.balance < 0).count(),
        'zero_balance_drivers': Driver.query.filter(Driver.balance == 0).count()
    }
    
    return jsonify({
        'success': True,
        'data': stats
    })

@api_bp.route('/stats/drivers/balances', methods=['GET'])
@require_auth
def api_get_driver_balances():
    """الحصول على أرصدة السائقين"""
    drivers = Driver.query.all()
    
    balances = [{
        'id': driver.id,
        'name': driver.name,
        'balance': float(driver.balance)
    } for driver in drivers]
    
    return jsonify({
        'success': True,
        'data': balances
    })

# ================================
# Health Check
# ================================

@api_bp.route('/health', methods=['GET'])
def api_health_check():
    """فحص صحة الـ API"""
    return jsonify({
        'success': True,
        'status': 'healthy',
        'timestamp': datetime.datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

# ================================
# API Documentation
# ================================

@api_bp.route('/docs', methods=['GET'])
def api_documentation():
    """توثيق الـ API"""
    docs = {
        'title': 'Driver Management System API',
        'version': '1.0.0',
        'description': 'REST API for managing drivers, vehicles, payments, and expenses',
        'base_url': '/api/v1',
        'authentication': {
            'type': 'Bearer Token (JWT)',
            'header': 'Authorization: Bearer <token>',
            'login_endpoint': '/api/v1/auth/login'
        },
        'endpoints': {
            'authentication': {
                'POST /auth/login': 'Login and get JWT token',
                'GET /auth/verify': 'Verify JWT token'
            },
            'drivers': {
                'GET /drivers': 'Get list of drivers',
                'GET /drivers/{id}': 'Get specific driver',
                'POST /drivers': 'Create new driver',
                'PUT /drivers/{id}': 'Update driver',
                'DELETE /drivers/{id}': 'Delete driver'
            },
            'vehicles': {
                'GET /vehicles': 'Get list of vehicles',
                'POST /vehicles': 'Create new vehicle'
            },
            'payments': {
                'GET /payments': 'Get list of payments',
                'POST /payments': 'Create new payment'
            },
            'statistics': {
                'GET /stats/summary': 'Get summary statistics',
                'GET /stats/drivers/balances': 'Get driver balances'
            },
            'system': {
                'GET /health': 'Health check'
            }
        }
    }
    
    return jsonify(docs)

def init_api(app):
    """تهيئة الـ API"""
    app.register_blueprint(api_bp)
    logger.info("API initialized successfully")