<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تقرير قابل للطباعة - نظام إدارة السائقين</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    @media print {
      .no-print { display: none !important; }
      body { font-size: 12px; }
      .page-break { page-break-before: always; }
    }
    .header-logo {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #007bff;
      padding-bottom: 20px;
    }
    .report-title {
      color: #007bff;
      font-weight: bold;
    }
    .summary-box {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 5px;
      padding: 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>

<div class="container-fluid">
  <!-- أزرار التحكم -->
  <div class="no-print mb-3">
    <button onclick="window.print()" class="btn btn-primary">
      <i class="fas fa-print"></i> طباعة
    </button>
    <button onclick="window.close()" class="btn btn-secondary">
      <i class="fas fa-times"></i> إغلاق
    </button>
  </div>

  <!-- رأس التقرير -->
  <div class="header-logo">
    <h2 class="report-title">نظام إدارة السائقين والمركبات</h2>
    <h4>{{ report_title }}</h4>
    <p class="text-muted">تاريخ التقرير: {{ current_date }}</p>
  </div>

  <!-- ملخص التقرير -->
  {% if summary %}
  <div class="summary-box">
    <h5>ملخص التقرير</h5>
    <div class="row">
      {% for key, value in summary.items() %}
      <div class="col-md-3 mb-2">
        <strong>{{ key }}:</strong> {{ value }}
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}

  <!-- بيانات السائقين -->
  {% if drivers %}
  <div class="mb-4">
    <h5>قائمة السائقين</h5>
    <table class="table table-bordered table-sm">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>الاسم</th>
          <th>رقم الهاتف</th>
          <th>الرصيد</th>
          <th>عدد المركبات</th>
          <th>آخر مدفوعة</th>
        </tr>
      </thead>
      <tbody>
        {% for driver in drivers %}
        <tr>
          <td>{{ loop.index }}</td>
          <td>{{ driver.name }}</td>
          <td>{{ driver.phone }}</td>
          <td class="{% if driver.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
            {{ "%.2f"|format(driver.balance) }}
          </td>
          <td>{{ driver.vehicles|length }}</td>
          <td>
            {% if driver.payments %}
              {{ driver.payments[-1].created_at.strftime('%Y-%m-%d') }}
            {% else %}
              -
            {% endif %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% endif %}

  <!-- بيانات المدفوعات -->
  {% if payments %}
  <div class="mb-4 page-break">
    <h5>المدفوعات</h5>
    <table class="table table-bordered table-sm">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>السائق</th>
          <th>المبلغ</th>
          <th>النوع</th>
          <th>الطريقة</th>
          <th>التاريخ</th>
          <th>الملاحظة</th>
        </tr>
      </thead>
      <tbody>
        {% for payment in payments %}
        <tr>
          <td>{{ loop.index }}</td>
          <td>{{ payment.driver.name }}</td>
          <td class="{% if payment.direction == 'in' %}text-success{% else %}text-danger{% endif %}">
            {% if payment.direction == 'in' %}+{% else %}-{% endif %}{{ "%.2f"|format(payment.amount) }}
          </td>
          <td>
            {% if payment.direction == 'in' %}
              <span class="badge bg-success">دخل</span>
            {% else %}
              <span class="badge bg-danger">خرج</span>
            {% endif %}
          </td>
          <td>{{ payment.method or '-' }}</td>
          <td>{{ payment.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
          <td>{{ payment.note or '-' }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% endif %}

  <!-- بيانات المصروفات -->
  {% if expenses %}
  <div class="mb-4 page-break">
    <h5>المصروفات</h5>
    <table class="table table-bordered table-sm">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>السائق</th>
          <th>المركبة</th>
          <th>المبلغ</th>
          <th>الفئة</th>
          <th>التاريخ</th>
          <th>الملاحظة</th>
        </tr>
      </thead>
      <tbody>
        {% for expense in expenses %}
        <tr>
          <td>{{ loop.index }}</td>
          <td>{{ expense.driver.name if expense.driver else '-' }}</td>
          <td>{{ expense.vehicle.plate_number if expense.vehicle else '-' }}</td>
          <td class="text-danger">{{ "%.2f"|format(expense.amount) }}</td>
          <td>{{ expense.category or '-' }}</td>
          <td>{{ expense.occurred_at.strftime('%Y-%m-%d') }}</td>
          <td>{{ expense.note or '-' }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% endif %}

  <!-- بيانات الرواتب -->
  {% if salaries %}
  <div class="mb-4 page-break">
    <h5>الرواتب</h5>
    <table class="table table-bordered table-sm">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>السائق</th>
          <th>المبلغ</th>
          <th>الشهر</th>
          <th>تاريخ الدفع</th>
          <th>الملاحظة</th>
        </tr>
      </thead>
      <tbody>
        {% for salary in salaries %}
        <tr>
          <td>{{ loop.index }}</td>
          <td>{{ salary.driver.name }}</td>
          <td class="text-success">{{ "%.2f"|format(salary.amount) }}</td>
          <td>{{ salary.month }}</td>
          <td>{{ salary.paid_at.strftime('%Y-%m-%d') if salary.paid_at else '-' }}</td>
          <td>{{ salary.note or '-' }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% endif %}

  <!-- تذييل التقرير -->
  <div class="mt-5 pt-3 border-top">
    <div class="row">
      <div class="col-md-6">
        <small class="text-muted">
          تم إنشاء هذا التقرير بواسطة نظام إدارة السائقين والمركبات<br>
          التاريخ: {{ current_date }}<br>
          الوقت: {{ current_time }}
        </small>
      </div>
      <div class="col-md-6 text-end">
        <small class="text-muted">
          التوقيع: ___________________<br>
          التاريخ: ___________________
        </small>
      </div>
    </div>
  </div>
</div>

<script>
// طباعة تلقائية عند فتح النافذة (اختياري)
// window.onload = function() { window.print(); }
</script>

</body>
</html>