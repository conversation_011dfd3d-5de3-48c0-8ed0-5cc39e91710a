@echo off
chcp 65001 > nul
title نظام إدارة السائقين والمركبات

echo ========================================
echo    نظام إدارة السائقين والمركبات
echo    Driver Management System
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    echo يرجى التأكد من تثبيت pip مع Python
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM التحقق من وجود البيئة الافتراضية
if not exist "venv" (
    echo 🔧 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء البيئة الافتراضية
)

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم تفعيل البيئة الافتراضية

REM تثبيت المتطلبات
if exist "requirements.txt" (
    echo 📦 تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المتطلبات
) else (
    echo ⚠️  ملف requirements.txt غير موجود
)

REM التحقق من وجود قاعدة البيانات
if not exist "drivers.db" (
    echo 🗄️  إعداد قاعدة البيانات...
    python init_db.py init
    if errorlevel 1 (
        echo ❌ فشل في إعداد قاعدة البيانات
        pause
        exit /b 1
    )
    echo ✅ تم إعداد قاعدة البيانات
)

echo.
echo 🚀 بدء تشغيل النظام...
echo 📍 الرابط: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo للإيقاف اضغط Ctrl+C
echo ========================================
echo.

REM تشغيل التطبيق
python app.py

echo.
echo تم إيقاف النظام
pause