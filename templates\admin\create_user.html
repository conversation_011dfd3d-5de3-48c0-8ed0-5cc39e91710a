{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h2>
        <a href="{{ url_for('user_management.users_list') }}" class="btn btn-secondary">
          <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
      </div>
    </div>
  </div>

  <!-- نموذج إضافة المستخدم -->
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-user"></i> بيانات المستخدم الجديد</h6>
        </div>
        <div class="card-body">
          <form id="createUserForm" method="POST">
            <div class="row">
              <!-- اسم المستخدم -->
              <div class="col-md-6 mb-3">
                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="username" name="username" required>
                <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
              </div>

              <!-- الاسم الكامل -->
              <div class="col-md-6 mb-3">
                <label for="full_name" class="form-label">الاسم الكامل</label>
                <input type="text" class="form-control" id="full_name" name="full_name">
              </div>
            </div>

            <div class="row">
              <!-- البريد الإلكتروني -->
              <div class="col-md-6 mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email">
                <div class="form-text">اختياري - يُستخدم لاستعادة كلمة المرور</div>
              </div>

              <!-- الدور -->
              <div class="col-md-6 mb-3">
                <label for="role_name" class="form-label">الدور <span class="text-danger">*</span></label>
                <select class="form-select" id="role_name" name="role_name" required>
                  <option value="">اختر الدور...</option>
                  {% for role in roles %}
                    <option value="{{ role.name }}">{{ role.display_name or role.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>

            <div class="row">
              <!-- كلمة المرور -->
              <div class="col-md-6 mb-3">
                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                <div class="input-group">
                  <input type="password" class="form-control" id="password" name="password" required>
                  <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                    <i class="fas fa-eye" id="password-icon"></i>
                  </button>
                </div>
                <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
              </div>

              <!-- تأكيد كلمة المرور -->
              <div class="col-md-6 mb-3">
                <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                <div class="input-group">
                  <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                  <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                    <i class="fas fa-eye" id="confirm_password-icon"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- معلومات الدور المختار -->
            <div id="roleInfo" class="alert alert-info" style="display: none;">
              <h6><i class="fas fa-info-circle"></i> معلومات الدور</h6>
              <p id="roleDescription"></p>
              <div id="rolePermissions"></div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="row">
              <div class="col-12">
                <hr>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save"></i> إنشاء المستخدم
                </button>
                <button type="reset" class="btn btn-outline-secondary">
                  <i class="fas fa-undo"></i> إعادة تعيين
                </button>
                <a href="{{ url_for('user_management.users_list') }}" class="btn btn-secondary">
                  <i class="fas fa-times"></i> إلغاء
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// معلومات الأدوار
const rolesInfo = {
  {% for role in roles %}
  '{{ role.name }}': {
    name: '{{ role.display_name or role.name }}',
    description: '{{ role.description or "بدون وصف" }}',
    permissions: {{ role.permissions|safe if role.permissions else '[]' }}
  }{% if not loop.last %},{% endif %}
  {% endfor %}
};

// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
  const field = document.getElementById(fieldId);
  const icon = document.getElementById(fieldId + '-icon');
  
  if (field.type === 'password') {
    field.type = 'text';
    icon.classList.remove('fa-eye');
    icon.classList.add('fa-eye-slash');
  } else {
    field.type = 'password';
    icon.classList.remove('fa-eye-slash');
    icon.classList.add('fa-eye');
  }
}

// عرض معلومات الدور عند التغيير
document.getElementById('role_name').addEventListener('change', function() {
  const roleName = this.value;
  const roleInfo = document.getElementById('roleInfo');
  
  if (roleName && rolesInfo[roleName]) {
    const role = rolesInfo[roleName];
    
    document.getElementById('roleDescription').textContent = role.description;
    
    // عرض الصلاحيات
    const permissionsDiv = document.getElementById('rolePermissions');
    if (role.permissions && role.permissions.length > 0) {
      let permissionsHtml = '<strong>الصلاحيات:</strong><br>';
      permissionsHtml += '<div class="row">';
      
      role.permissions.forEach((permission, index) => {
        if (index % 3 === 0 && index > 0) {
          permissionsHtml += '</div><div class="row">';
        }
        permissionsHtml += `<div class="col-md-4"><small class="badge bg-secondary me-1">${permission}</small></div>`;
      });
      
      permissionsHtml += '</div>';
      permissionsDiv.innerHTML = permissionsHtml;
    } else {
      permissionsDiv.innerHTML = '<small class="text-muted">لا توجد صلاحيات محددة</small>';
    }
    
    roleInfo.style.display = 'block';
  } else {
    roleInfo.style.display = 'none';
  }
});

// التحقق من صحة النموذج
document.getElementById('createUserForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const password = formData.get('password');
  const confirmPassword = formData.get('confirm_password');
  
  // التحقق من كلمة المرور
  if (password.length < 6) {
    alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    return;
  }
  
  if (password !== confirmPassword) {
    alert('كلمة المرور وتأكيدها غير متطابقين');
    return;
  }
  
  // إرسال البيانات
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
  submitBtn.disabled = true;
  
  fetch(this.action, {
    method: 'POST',
    body: formData
  })
  .then(response => {
    if (response.redirected) {
      window.location.href = response.url;
    } else {
      return response.json();
    }
  })
  .then(data => {
    if (data && data.success) {
      alert('تم إنشاء المستخدم بنجاح');
      window.location.href = "{{ url_for('user_management.users_list') }}";
    } else if (data && data.error) {
      alert('خطأ: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في إنشاء المستخدم');
  })
  .finally(() => {
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

// التحقق من اسم المستخدم أثناء الكتابة
document.getElementById('username').addEventListener('blur', function() {
  const username = this.value.trim();
  
  if (username) {
    // التحقق من عدم وجود مسافات
    if (username.includes(' ')) {
      this.setCustomValidity('اسم المستخدم لا يجب أن يحتوي على مسافات');
      this.reportValidity();
    } else {
      this.setCustomValidity('');
    }
  }
});

// التحقق من البريد الإلكتروني
document.getElementById('email').addEventListener('blur', function() {
  const email = this.value.trim();
  
  if (email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      this.setCustomValidity('البريد الإلكتروني غير صحيح');
      this.reportValidity();
    } else {
      this.setCustomValidity('');
    }
  }
});
</script>
{% endblock %}