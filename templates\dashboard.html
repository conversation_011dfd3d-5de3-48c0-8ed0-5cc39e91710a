<!-- templates/dashboard.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>لوحة التحكم - السائقون</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <span class="navbar-brand">لوحة التحكم</span>
    <div class="d-flex">
      <div class="dropdown me-2">
        <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
          إدارة
        </button>
        <ul class="dropdown-menu">
          <li><a class="dropdown-item" href="{{ url_for('add_driver') }}">إضافة سائق</a></li>
          <li><a class="dropdown-item" href="{{ url_for('vehicles') }}">المركبات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('payments') }}">المدفوعات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('salaries') }}">الرواتب</a></li>
          <li><a class="dropdown-item" href="{{ url_for('expenses') }}">المصروفات</a></li>
        </ul>
      </div>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('analytics') }}" class="btn btn-outline-light me-2">الإحصائيات</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light me-2">الإعدادات</a>
      <a href="{{ url_for('logout') }}" class="btn btn-outline-light">خروج</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <!-- إحصائيات سريعة -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ drivers|length }}</h4>
              <small>إجمالي السائقين</small>
            </div>
            <i class="fas fa-users fa-2x opacity-75"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ drivers|selectattr('balance', '>', 0)|list|length }}</h4>
              <small>رصيد موجب</small>
            </div>
            <i class="fas fa-arrow-up fa-2x opacity-75"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-danger text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ drivers|selectattr('balance', '<', 0)|list|length }}</h4>
              <small>رصيد سالب</small>
            </div>
            <i class="fas fa-arrow-down fa-2x opacity-75"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ "%.0f"|format(drivers|sum(attribute='balance')) }}</h4>
              <small>إجمالي الأرصدة</small>
            </div>
            <i class="fas fa-calculator fa-2x opacity-75"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- الإشعارات -->
  {% if notifications %}
    <div class="row mb-4">
      {% for notification in notifications %}
        <div class="col-md-6 mb-2">
          <div class="alert alert-{{ notification.type }} d-flex align-items-center" role="alert">
            <i class="{{ notification.icon }} me-2"></i>
            {{ notification.message }}
          </div>
        </div>
      {% endfor %}
    </div>
  {% endif %}

  <div class="card shadow-sm">
    <div class="card-body">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 class="mb-0">قائمة السائقين ({{ drivers|length }})</h4>
        <div class="d-flex gap-2">
          <div class="dropdown">
            <button class="btn btn-outline-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="fas fa-download"></i> تصدير
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="{{ url_for('export_drivers') }}">تصدير جميع السائقين</a></li>
              <li><a class="dropdown-item" href="{{ url_for('export_drivers', format='filtered') }}">تصدير النتائج المفلترة</a></li>
            </ul>
          </div>
          <form method="get" class="d-flex">
            <input type="text" class="form-control me-2" name="search" placeholder="البحث بالاسم أو الهاتف..." value="{{ search or '' }}" style="width: 250px;">
            <button type="submit" class="btn btn-outline-primary">بحث</button>
            {% if search %}
              <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary ms-2">إلغاء</a>
            {% endif %}
          </form>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-striped align-middle">
          <thead>
            <tr>
              <th>#</th>
              <th>الاسم</th>
              <th>رقم الهاتف</th>
              <th>الرصيد</th>
              <th class="text-center">إجراءات</th>
            </tr>
          </thead>
          <tbody>
            {% for d in drivers %}
              <tr>
                <td>{{ d.id }}</td>
                <td><a href="{{ url_for('driver_detail', driver_id=d.id) }}">{{ d.name }}</a></td>
                <td>{{ d.phone }}</td>
                <td>{{ "%.2f"|format(d.balance) }}</td>
                <td class="text-center">
                  <a href="{{ url_for('edit_driver', driver_id=d.id) }}" class="btn btn-sm btn-warning">تعديل</a>
                  <form action="{{ url_for('delete_driver', driver_id=d.id) }}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('تأكيد حذف السائق؟');">حذف</button>
                  </form>
                </td>
              </tr>
            {% else %}
              <tr>
                <td colspan="5" class="text-center text-muted">لا توجد بيانات حتى الآن.</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>