#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الإشعارات والتنبيهات
Notifications and Alerts System

يحتوي على وظائف إرسال الإشعارات عبر:
- البريد الإلكتروني
- الرسائل النصية (SMS)
- الإشعارات الداخلية
- التنبيهات التلقائية
"""

import smtplib
import os
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import json
import logging

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NotificationManager:
    """مدير الإشعارات"""
    
    def __init__(self, app=None):
        self.app = app
        self.notifications = []
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير الإشعارات مع التطبيق"""
        self.app = app
        
        # إعدادات البريد الإلكتروني
        self.mail_server = app.config.get('MAIL_SERVER')
        self.mail_port = app.config.get('MAIL_PORT', 587)
        self.mail_use_tls = app.config.get('MAIL_USE_TLS', True)
        self.mail_username = app.config.get('MAIL_USERNAME')
        self.mail_password = app.config.get('MAIL_PASSWORD')
        
        # إنشاء مجلد الإشعارات
        self.notifications_dir = os.path.join(os.path.dirname(__file__), 'notifications')
        os.makedirs(self.notifications_dir, exist_ok=True)
    
    def add_notification(self, title, message, type='info', user_id=None, data=None):
        """إضافة إشعار جديد"""
        notification = {
            'id': len(self.notifications) + 1,
            'title': title,
            'message': message,
            'type': type,  # info, success, warning, error
            'user_id': user_id,
            'data': data or {},
            'created_at': datetime.now().isoformat(),
            'read': False
        }
        
        self.notifications.append(notification)
        
        # حفظ الإشعار في ملف
        self._save_notification(notification)
        
        logger.info(f"تم إضافة إشعار جديد: {title}")
        return notification
    
    def get_notifications(self, user_id=None, unread_only=False):
        """الحصول على الإشعارات"""
        notifications = self.notifications.copy()
        
        if user_id:
            notifications = [n for n in notifications if n.get('user_id') == user_id or n.get('user_id') is None]
        
        if unread_only:
            notifications = [n for n in notifications if not n.get('read', False)]
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        notifications.sort(key=lambda x: x['created_at'], reverse=True)
        
        return notifications
    
    def mark_as_read(self, notification_id, user_id=None):
        """تمييز الإشعار كمقروء"""
        for notification in self.notifications:
            if notification['id'] == notification_id:
                if user_id is None or notification.get('user_id') == user_id:
                    notification['read'] = True
                    self._save_notification(notification)
                    return True
        return False
    
    def _save_notification(self, notification):
        """حفظ الإشعار في ملف"""
        try:
            filename = f"notification_{notification['id']}.json"
            filepath = os.path.join(self.notifications_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(notification, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ الإشعار: {e}")
    
    def load_notifications(self):
        """تحميل الإشعارات من الملفات"""
        try:
            if not os.path.exists(self.notifications_dir):
                return
            
            for filename in os.listdir(self.notifications_dir):
                if filename.startswith('notification_') and filename.endswith('.json'):
                    filepath = os.path.join(self.notifications_dir, filename)
                    
                    with open(filepath, 'r', encoding='utf-8') as f:
                        notification = json.load(f)
                        
                    # التحقق من عدم وجود الإشعار مسبقاً
                    if not any(n['id'] == notification['id'] for n in self.notifications):
                        self.notifications.append(notification)
        except Exception as e:
            logger.error(f"خطأ في تحميل الإشعارات: {e}")
    
    def send_email(self, to_email, subject, body, attachments=None):
        """إرسال بريد إلكتروني"""
        if not self.mail_server or not self.mail_username or not self.mail_password:
            logger.warning("إعدادات البريد الإلكتروني غير مكتملة")
            return False
        
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart()
            msg['From'] = self.mail_username
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # إضافة النص
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # إضافة المرفقات
            if attachments:
                for attachment_path in attachments:
                    if os.path.exists(attachment_path):
                        with open(attachment_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(attachment_path)}'
                        )
                        msg.attach(part)
            
            # إرسال الرسالة
            server = smtplib.SMTP(self.mail_server, self.mail_port)
            if self.mail_use_tls:
                server.starttls()
            
            server.login(self.mail_username, self.mail_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"تم إرسال بريد إلكتروني إلى: {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد الإلكتروني: {e}")
            return False
    
    def check_balance_alerts(self, drivers):
        """فحص تنبيهات الأرصدة"""
        alerts = []
        
        for driver in drivers:
            # تنبيه للأرصدة السالبة
            if driver.balance < 0:
                alert = {
                    'type': 'warning',
                    'title': 'رصيد سالب',
                    'message': f'السائق {driver.name} لديه رصيد سالب: {driver.balance:.2f} ريال',
                    'driver_id': driver.id,
                    'balance': driver.balance
                }
                alerts.append(alert)
                
                # إضافة إشعار
                self.add_notification(
                    title='تنبيه: رصيد سالب',
                    message=f'السائق {driver.name} لديه رصيد سالب قدره {abs(driver.balance):.2f} ريال',
                    type='warning',
                    data={'driver_id': driver.id, 'balance': driver.balance}
                )
            
            # تنبيه للأرصدة المنخفضة (أقل من 100 ريال)
            elif 0 < driver.balance < 100:
                alert = {
                    'type': 'info',
                    'title': 'رصيد منخفض',
                    'message': f'السائق {driver.name} لديه رصيد منخفض: {driver.balance:.2f} ريال',
                    'driver_id': driver.id,
                    'balance': driver.balance
                }
                alerts.append(alert)
        
        return alerts
    
    def check_payment_alerts(self, payments):
        """فحص تنبيهات المدفوعات"""
        alerts = []
        
        # البحث عن مدفوعات كبيرة (أكثر من 5000 ريال)
        large_payments = [p for p in payments if p.amount > 5000]
        
        for payment in large_payments:
            alert = {
                'type': 'info',
                'title': 'مدفوعة كبيرة',
                'message': f'مدفوعة كبيرة بقيمة {payment.amount:.2f} ريال للسائق {payment.driver.name}',
                'payment_id': payment.id,
                'amount': payment.amount
            }
            alerts.append(alert)
            
            # إضافة إشعار
            self.add_notification(
                title='مدفوعة كبيرة',
                message=f'تم تسجيل مدفوعة كبيرة بقيمة {payment.amount:.2f} ريال للسائق {payment.driver.name}',
                type='info',
                data={'payment_id': payment.id, 'amount': payment.amount}
            )
        
        return alerts
    
    def check_vehicle_alerts(self, vehicles):
        """فحص تنبيهات المركبات"""
        alerts = []
        
        # فحص المركبات القديمة (أكثر من 15 سنة)
        current_year = datetime.now().year
        
        for vehicle in vehicles:
            if vehicle.year and (current_year - vehicle.year) > 15:
                alert = {
                    'type': 'warning',
                    'title': 'مركبة قديمة',
                    'message': f'المركبة {vehicle.plate_number} قديمة ({vehicle.year}) وقد تحتاج صيانة',
                    'vehicle_id': vehicle.id,
                    'age': current_year - vehicle.year
                }
                alerts.append(alert)
        
        return alerts
    
    def send_daily_report(self, admin_email, stats):
        """إرسال تقرير يومي"""
        if not admin_email:
            return False
        
        # إنشاء محتوى التقرير
        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; }}
                .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .stat {{ background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .stat h3 {{ margin: 0; color: #007bff; }}
                .stat p {{ margin: 5px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>التقرير اليومي - نظام إدارة السائقين</h1>
                <p>{datetime.now().strftime('%Y-%m-%d')}</p>
            </div>
            
            <div class="content">
                <div class="stat">
                    <h3>إجمالي السائقين</h3>
                    <p>{stats.get('drivers_count', 0)} سائق</p>
                </div>
                
                <div class="stat">
                    <h3>إجمالي المركبات</h3>
                    <p>{stats.get('vehicles_count', 0)} مركبة</p>
                </div>
                
                <div class="stat">
                    <h3>إجمالي المدفوعات اليوم</h3>
                    <p>{stats.get('today_payments', 0)} مدفوعة</p>
                </div>
                
                <div class="stat">
                    <h3>إجمالي المبلغ اليوم</h3>
                    <p>{stats.get('today_amount', 0):.2f} ريال</p>
                </div>
                
                <div class="stat">
                    <h3>الأرصدة الموجبة</h3>
                    <p>{stats.get('positive_balance_count', 0)} سائق</p>
                </div>
                
                <div class="stat">
                    <h3>الأرصدة السالبة</h3>
                    <p>{stats.get('negative_balance_count', 0)} سائق</p>
                </div>
            </div>
            
            <div style="text-align: center; padding: 20px; color: #666;">
                <p>تم إنشاء هذا التقرير تلقائياً من نظام إدارة السائقين والمركبات</p>
            </div>
        </body>
        </html>
        """
        
        # إرسال التقرير
        success = self.send_email(
            to_email=admin_email,
            subject=f"التقرير اليومي - {datetime.now().strftime('%Y-%m-%d')}",
            body=html_content
        )
        
        if success:
            self.add_notification(
                title='تم إرسال التقرير اليومي',
                message=f'تم إرسال التقرير اليومي إلى {admin_email}',
                type='success'
            )
        
        return success
    
    def cleanup_old_notifications(self, days=30):
        """تنظيف الإشعارات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # إزالة الإشعارات القديمة من الذاكرة
        self.notifications = [
            n for n in self.notifications 
            if datetime.fromisoformat(n['created_at']) > cutoff_date
        ]
        
        # إزالة ملفات الإشعارات القديمة
        try:
            for filename in os.listdir(self.notifications_dir):
                if filename.startswith('notification_') and filename.endswith('.json'):
                    filepath = os.path.join(self.notifications_dir, filename)
                    
                    # فحص تاريخ الملف
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    if file_time < cutoff_date:
                        os.remove(filepath)
                        logger.info(f"تم حذف الإشعار القديم: {filename}")
        except Exception as e:
            logger.error(f"خطأ في تنظيف الإشعارات القديمة: {e}")

# إنشاء مثيل مدير الإشعارات
notification_manager = NotificationManager()

def init_notifications(app):
    """تهيئة نظام الإشعارات"""
    notification_manager.init_app(app)
    notification_manager.load_notifications()
    
    # تنظيف الإشعارات القديمة عند البدء
    notification_manager.cleanup_old_notifications()
    
    return notification_manager