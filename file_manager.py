#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الملفات والمرفقات
File Management System

يوفر:
- رفع الملفات وإدارتها
- معاينة الملفات
- ضغط وأرشفة الملفات
- إدارة المساحة التخزينية
- أمان الملفات
"""

import os
import shutil
import zipfile
import mimetypes
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage
from PIL import Image
import hashlib
import logging
from pathlib import Path
import json

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileManager:
    """مدير الملفات"""
    
    def __init__(self, app=None, upload_folder='uploads'):
        self.app = app
        self.upload_folder = upload_folder
        self.max_file_size = 16 * 1024 * 1024  # 16MB
        self.allowed_extensions = {
            'images': {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'},
            'documents': {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'},
            'archives': {'zip', 'rar', '7z', 'tar', 'gz'},
            'videos': {'mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'},
            'audio': {'mp3', 'wav', 'flac', 'aac', 'ogg'}
        }
        
        # إنشاء مجلدات التخزين
        self._create_directories()
    
    def _create_directories(self):
        """إنشاء مجلدات التخزين"""
        directories = [
            self.upload_folder,
            os.path.join(self.upload_folder, 'images'),
            os.path.join(self.upload_folder, 'documents'),
            os.path.join(self.upload_folder, 'archives'),
            os.path.join(self.upload_folder, 'videos'),
            os.path.join(self.upload_folder, 'audio'),
            os.path.join(self.upload_folder, 'temp'),
            os.path.join(self.upload_folder, 'thumbnails')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_file_category(self, filename):
        """تحديد فئة الملف"""
        extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        
        for category, extensions in self.allowed_extensions.items():
            if extension in extensions:
                return category
        
        return 'other'
    
    def is_allowed_file(self, filename):
        """التحقق من أن الملف مسموح"""
        if not filename or '.' not in filename:
            return False
        
        extension = filename.rsplit('.', 1)[1].lower()
        all_extensions = set()
        
        for extensions in self.allowed_extensions.values():
            all_extensions.update(extensions)
        
        return extension in all_extensions
    
    def generate_unique_filename(self, filename):
        """إنشاء اسم ملف فريد"""
        name, ext = os.path.splitext(secure_filename(filename))
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = hashlib.md5(f"{name}{timestamp}".encode()).hexdigest()[:8]
        
        return f"{name}_{timestamp}_{unique_id}{ext}"
    
    def upload_file(self, file, category=None, description=None):
        """رفع ملف"""
        try:
            if not file or not file.filename:
                return {'success': False, 'error': 'لم يتم اختيار ملف'}
            
            if not self.is_allowed_file(file.filename):
                return {'success': False, 'error': 'نوع الملف غير مسموح'}
            
            # التحقق من حجم الملف
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
            
            if file_size > self.max_file_size:
                return {'success': False, 'error': f'حجم الملف كبير جداً (الحد الأقصى: {self.max_file_size // 1024 // 1024}MB)'}
            
            # تحديد الفئة
            if not category:
                category = self.get_file_category(file.filename)
            
            # إنشاء اسم ملف فريد
            unique_filename = self.generate_unique_filename(file.filename)
            
            # تحديد مسار الحفظ
            category_folder = os.path.join(self.upload_folder, category)
            file_path = os.path.join(category_folder, unique_filename)
            
            # حفظ الملف
            file.save(file_path)
            
            # إنشاء صورة مصغرة للصور
            thumbnail_path = None
            if category == 'images':
                thumbnail_path = self._create_thumbnail(file_path, unique_filename)
            
            # حساب hash الملف
            file_hash = self._calculate_file_hash(file_path)
            
            # معلومات الملف
            file_info = {
                'original_name': file.filename,
                'filename': unique_filename,
                'file_path': file_path,
                'category': category,
                'size': file_size,
                'mime_type': mimetypes.guess_type(file.filename)[0],
                'hash': file_hash,
                'thumbnail': thumbnail_path,
                'description': description,
                'uploaded_at': datetime.utcnow().isoformat(),
                'url': f'/uploads/{category}/{unique_filename}'
            }
            
            # حفظ معلومات الملف
            self._save_file_metadata(unique_filename, file_info)
            
            logger.info(f"تم رفع الملف: {file.filename} -> {unique_filename}")
            
            return {
                'success': True,
                'message': 'تم رفع الملف بنجاح',
                'file_info': file_info
            }
            
        except Exception as e:
            logger.error(f"خطأ في رفع الملف: {e}")
            return {'success': False, 'error': str(e)}
    
    def _create_thumbnail(self, image_path, filename):
        """إنشاء صورة مصغرة"""
        try:
            thumbnail_size = (200, 200)
            thumbnail_filename = f"thumb_{filename}"
            thumbnail_path = os.path.join(self.upload_folder, 'thumbnails', thumbnail_filename)
            
            with Image.open(image_path) as img:
                # تحويل إلى RGB إذا كانت RGBA
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                
                img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=85)
            
            return thumbnail_path
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الصورة المصغرة: {e}")
            return None
    
    def _calculate_file_hash(self, file_path):
        """حساب hash الملف"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None
    
    def _save_file_metadata(self, filename, metadata):
        """حفظ معلومات الملف"""
        metadata_file = os.path.join(self.upload_folder, 'metadata.json')
        
        try:
            # قراءة البيانات الموجودة
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    all_metadata = json.load(f)
            else:
                all_metadata = {}
            
            # إضافة البيانات الجديدة
            all_metadata[filename] = metadata
            
            # حفظ البيانات
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(all_metadata, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ معلومات الملف: {e}")
    
    def get_file_metadata(self, filename):
        """الحصول على معلومات الملف"""
        metadata_file = os.path.join(self.upload_folder, 'metadata.json')
        
        try:
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    all_metadata = json.load(f)
                    return all_metadata.get(filename)
        except:
            pass
        
        return None
    
    def list_files(self, category=None, page=1, per_page=20):
        """قائمة الملفات"""
        metadata_file = os.path.join(self.upload_folder, 'metadata.json')
        
        try:
            if not os.path.exists(metadata_file):
                return {'files': [], 'total': 0, 'pages': 0}
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                all_metadata = json.load(f)
            
            # فلترة حسب الفئة
            files = []
            for filename, metadata in all_metadata.items():
                if category is None or metadata.get('category') == category:
                    # التحقق من وجود الملف
                    if os.path.exists(metadata.get('file_path', '')):
                        files.append(metadata)
            
            # ترتيب حسب تاريخ الرفع
            files.sort(key=lambda x: x.get('uploaded_at', ''), reverse=True)
            
            # تقسيم الصفحات
            total = len(files)
            pages = (total + per_page - 1) // per_page
            start = (page - 1) * per_page
            end = start + per_page
            
            return {
                'files': files[start:end],
                'total': total,
                'pages': pages,
                'current_page': page
            }
            
        except Exception as e:
            logger.error(f"خطأ في قراءة قائمة الملفات: {e}")
            return {'files': [], 'total': 0, 'pages': 0}
    
    def delete_file(self, filename):
        """حذف ملف"""
        try:
            metadata = self.get_file_metadata(filename)
            if not metadata:
                return {'success': False, 'error': 'الملف غير موجود'}
            
            # حذف الملف الأصلي
            file_path = metadata.get('file_path')
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
            
            # حذف الصورة المصغرة
            thumbnail_path = metadata.get('thumbnail')
            if thumbnail_path and os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)
            
            # حذف من البيانات الوصفية
            self._remove_file_metadata(filename)
            
            logger.info(f"تم حذف الملف: {filename}")
            
            return {'success': True, 'message': 'تم حذف الملف بنجاح'}
            
        except Exception as e:
            logger.error(f"خطأ في حذف الملف: {e}")
            return {'success': False, 'error': str(e)}
    
    def _remove_file_metadata(self, filename):
        """حذف معلومات الملف"""
        metadata_file = os.path.join(self.upload_folder, 'metadata.json')
        
        try:
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    all_metadata = json.load(f)
                
                if filename in all_metadata:
                    del all_metadata[filename]
                    
                    with open(metadata_file, 'w', encoding='utf-8') as f:
                        json.dump(all_metadata, f, ensure_ascii=False, indent=2)
                        
        except Exception as e:
            logger.error(f"خطأ في حذف معلومات الملف: {e}")
    
    def get_storage_stats(self):
        """إحصائيات التخزين"""
        try:
            total_size = 0
            file_count = 0
            category_stats = {}
            
            metadata_file = os.path.join(self.upload_folder, 'metadata.json')
            
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    all_metadata = json.load(f)
                
                for filename, metadata in all_metadata.items():
                    if os.path.exists(metadata.get('file_path', '')):
                        size = metadata.get('size', 0)
                        category = metadata.get('category', 'other')
                        
                        total_size += size
                        file_count += 1
                        
                        if category not in category_stats:
                            category_stats[category] = {'count': 0, 'size': 0}
                        
                        category_stats[category]['count'] += 1
                        category_stats[category]['size'] += size
            
            # حساب المساحة المتاحة
            disk_usage = shutil.disk_usage(self.upload_folder)
            available_space = disk_usage.free
            
            return {
                'total_files': file_count,
                'total_size': total_size,
                'available_space': available_space,
                'categories': category_stats,
                'upload_folder': self.upload_folder
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات التخزين: {e}")
            return {
                'total_files': 0,
                'total_size': 0,
                'available_space': 0,
                'categories': {},
                'upload_folder': self.upload_folder
            }
    
    def create_archive(self, filenames, archive_name=None):
        """إنشاء أرشيف من الملفات المحددة"""
        try:
            if not filenames:
                return {'success': False, 'error': 'لم يتم تحديد ملفات'}
            
            if not archive_name:
                archive_name = f"archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            
            archive_path = os.path.join(self.upload_folder, 'archives', archive_name)
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for filename in filenames:
                    metadata = self.get_file_metadata(filename)
                    if metadata and os.path.exists(metadata.get('file_path', '')):
                        file_path = metadata['file_path']
                        original_name = metadata['original_name']
                        zipf.write(file_path, original_name)
            
            # حساب حجم الأرشيف
            archive_size = os.path.getsize(archive_path)
            
            # حفظ معلومات الأرشيف
            archive_info = {
                'original_name': archive_name,
                'filename': archive_name,
                'file_path': archive_path,
                'category': 'archives',
                'size': archive_size,
                'mime_type': 'application/zip',
                'hash': self._calculate_file_hash(archive_path),
                'thumbnail': None,
                'description': f'أرشيف يحتوي على {len(filenames)} ملف',
                'uploaded_at': datetime.utcnow().isoformat(),
                'url': f'/uploads/archives/{archive_name}'
            }
            
            self._save_file_metadata(archive_name, archive_info)
            
            logger.info(f"تم إنشاء الأرشيف: {archive_name}")
            
            return {
                'success': True,
                'message': 'تم إنشاء الأرشيف بنجاح',
                'archive_info': archive_info
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الأرشيف: {e}")
            return {'success': False, 'error': str(e)}
    
    def cleanup_old_files(self, days=30):
        """تنظيف الملفات القديمة"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            deleted_count = 0
            freed_space = 0
            
            metadata_file = os.path.join(self.upload_folder, 'metadata.json')
            
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    all_metadata = json.load(f)
                
                files_to_delete = []
                
                for filename, metadata in all_metadata.items():
                    uploaded_at = datetime.fromisoformat(metadata.get('uploaded_at', ''))
                    if uploaded_at < cutoff_date:
                        files_to_delete.append(filename)
                
                for filename in files_to_delete:
                    metadata = all_metadata[filename]
                    freed_space += metadata.get('size', 0)
                    
                    result = self.delete_file(filename)
                    if result['success']:
                        deleted_count += 1
            
            logger.info(f"تم حذف {deleted_count} ملف قديم، تم توفير {freed_space} بايت")
            
            return {
                'success': True,
                'deleted_count': deleted_count,
                'freed_space': freed_space
            }
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الملفات القديمة: {e}")
            return {'success': False, 'error': str(e)}

def init_file_manager(app):
    """تهيئة مدير الملفات"""
    upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
    file_manager = FileManager(app, upload_folder)
    
    # إضافة route لتقديم الملفات
    @app.route('/uploads/<category>/<filename>')
    def uploaded_file(category, filename):
        from flask import send_from_directory
        category_folder = os.path.join(file_manager.upload_folder, category)
        return send_from_directory(category_folder, filename)
    
    logger.info("تم تهيئة نظام إدارة الملفات")
    return file_manager