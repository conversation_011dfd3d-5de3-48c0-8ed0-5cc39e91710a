# مثال على ملف متغيرات البيئة
# انسخ هذا الملف إلى .env وعدل القيم حسب الحاجة

# إعدادات Flask
FLASK_APP=app.py
FLASK_ENV=production
SECRET_KEY=your-secret-key-here-change-this-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///drivers.db
# أو للاستخدام مع PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost/drivers_db
# أو للاستخدام مع MySQL:
# DATABASE_URL=mysql://username:password@localhost/drivers_db

# إعدادات الأمان
WTF_CSRF_ENABLED=true
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true

# إعدادات البريد الإلكتروني (اختياري)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# إعدادات التخزين
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# إعدادات النسخ الاحتياطية
BACKUP_FOLDER=backups
AUTO_BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30

# إعدادات التصدير
EXPORT_FOLDER=exports
EXPORT_RETENTION_HOURS=24

# إعدادات التطبيق
APP_NAME=نظام إدارة السائقين
APP_VERSION=1.0.0
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# إعدادات الأداء
SQLALCHEMY_POOL_SIZE=10
SQLALCHEMY_POOL_TIMEOUT=20
SQLALCHEMY_POOL_RECYCLE=3600

# إعدادات السجلات
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# إعدادات Redis (للتخزين المؤقت - اختياري)
REDIS_URL=redis://localhost:6379/0

# إعدادات Celery (للمهام الخلفية - اختياري)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0