#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المهام والجدولة
Task Management and Scheduling System

يوفر:
- إدارة المهام المجدولة
- تنفيذ المهام في الخلفية
- مراقبة حالة المهام
- إعادة تشغيل المهام الفاشلة
- سجلات تفصيلية للمهام
"""

import os
import json
import threading
import time
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Callable, Any
import logging
import sqlite3
from contextlib import contextmanager

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """حالات المهام"""
    PENDING = "pending"      # في الانتظار
    RUNNING = "running"      # قيد التنفيذ
    COMPLETED = "completed"  # مكتملة
    FAILED = "failed"        # فاشلة
    CANCELLED = "cancelled"  # ملغية
    RETRYING = "retrying"    # إعادة محاولة

class TaskPriority(Enum):
    """أولويات المهام"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Task:
    """نموذج المهمة"""
    id: str
    name: str
    description: str
    function_name: str
    args: List[Any]
    kwargs: Dict[str, Any]
    priority: TaskPriority
    scheduled_time: datetime
    max_retries: int = 3
    retry_delay: int = 60  # بالثواني
    timeout: int = 300     # بالثواني
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    result: Optional[Any] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()

class TaskManager:
    """مدير المهام"""
    
    def __init__(self, db_path='tasks.db'):
        self.db_path = db_path
        self.tasks: Dict[str, Task] = {}
        self.task_functions: Dict[str, Callable] = {}
        self.running = False
        self.worker_thread = None
        self.lock = threading.Lock()
        
        # إنشاء قاعدة البيانات
        self._init_database()
        
        # تحميل المهام المحفوظة
        self._load_tasks()
        
        # تسجيل المهام الافتراضية
        self._register_default_tasks()
    
    def _init_database(self):
        """تهيئة قاعدة البيانات"""
        with self._get_db_connection() as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    function_name TEXT NOT NULL,
                    args TEXT,
                    kwargs TEXT,
                    priority INTEGER,
                    scheduled_time TEXT,
                    max_retries INTEGER,
                    retry_delay INTEGER,
                    timeout INTEGER,
                    status TEXT,
                    created_at TEXT,
                    started_at TEXT,
                    completed_at TEXT,
                    error_message TEXT,
                    retry_count INTEGER,
                    result TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS task_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT,
                    timestamp TEXT,
                    level TEXT,
                    message TEXT,
                    FOREIGN KEY (task_id) REFERENCES tasks (id)
                )
            ''')
    
    @contextmanager
    def _get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def _load_tasks(self):
        """تحميل المهام من قاعدة البيانات"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.execute('SELECT * FROM tasks WHERE status IN (?, ?)', 
                                    (TaskStatus.PENDING.value, TaskStatus.RETRYING.value))
                
                for row in cursor.fetchall():
                    task = Task(
                        id=row['id'],
                        name=row['name'],
                        description=row['description'],
                        function_name=row['function_name'],
                        args=json.loads(row['args']) if row['args'] else [],
                        kwargs=json.loads(row['kwargs']) if row['kwargs'] else {},
                        priority=TaskPriority(row['priority']),
                        scheduled_time=datetime.fromisoformat(row['scheduled_time']),
                        max_retries=row['max_retries'],
                        retry_delay=row['retry_delay'],
                        timeout=row['timeout'],
                        status=TaskStatus(row['status']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        started_at=datetime.fromisoformat(row['started_at']) if row['started_at'] else None,
                        completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
                        error_message=row['error_message'],
                        retry_count=row['retry_count'],
                        result=json.loads(row['result']) if row['result'] else None
                    )
                    
                    self.tasks[task.id] = task
                    
            logger.info(f"تم تحميل {len(self.tasks)} مهمة من قاعدة البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل المهام: {e}")
    
    def _save_task(self, task: Task):
        """حفظ المهمة في قاعدة البيانات"""
        try:
            with self._get_db_connection() as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO tasks 
                    (id, name, description, function_name, args, kwargs, priority, 
                     scheduled_time, max_retries, retry_delay, timeout, status, 
                     created_at, started_at, completed_at, error_message, retry_count, result)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task.id, task.name, task.description, task.function_name,
                    json.dumps(task.args), json.dumps(task.kwargs), task.priority.value,
                    task.scheduled_time.isoformat(), task.max_retries, task.retry_delay,
                    task.timeout, task.status.value, task.created_at.isoformat(),
                    task.started_at.isoformat() if task.started_at else None,
                    task.completed_at.isoformat() if task.completed_at else None,
                    task.error_message, task.retry_count,
                    json.dumps(task.result) if task.result else None
                ))
                
        except Exception as e:
            logger.error(f"خطأ في حفظ المهمة {task.id}: {e}")
    
    def _log_task_event(self, task_id: str, level: str, message: str):
        """تسجيل حدث المهمة"""
        try:
            with self._get_db_connection() as conn:
                conn.execute('''
                    INSERT INTO task_logs (task_id, timestamp, level, message)
                    VALUES (?, ?, ?, ?)
                ''', (task_id, datetime.utcnow().isoformat(), level, message))
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل حدث المهمة: {e}")
    
    def register_task_function(self, name: str, function: Callable):
        """تسجيل دالة مهمة"""
        self.task_functions[name] = function
        logger.info(f"تم تسجيل دالة المهمة: {name}")
    
    def _register_default_tasks(self):
        """تسجيل المهام الافتراضية"""
        
        def cleanup_old_logs():
            """تنظيف السجلات القديمة"""
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=30)
                with self._get_db_connection() as conn:
                    cursor = conn.execute('DELETE FROM task_logs WHERE timestamp < ?', 
                                        (cutoff_date.isoformat(),))
                    deleted_count = cursor.rowcount
                
                logger.info(f"تم حذف {deleted_count} سجل قديم")
                return {'deleted_count': deleted_count}
                
            except Exception as e:
                logger.error(f"خطأ في تنظيف السجلات: {e}")
                raise
        
        def backup_database():
            """نسخ احتياطية لقاعدة البيانات"""
            try:
                backup_path = f"backup_tasks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                
                with self._get_db_connection() as source:
                    backup_conn = sqlite3.connect(backup_path)
                    source.backup(backup_conn)
                    backup_conn.close()
                
                logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
                return {'backup_path': backup_path}
                
            except Exception as e:
                logger.error(f"خطأ في النسخ الاحتياطي: {e}")
                raise
        
        def system_health_check():
            """فحص صحة النظام"""
            try:
                import psutil
                
                # فحص استخدام المعالج
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # فحص استخدام الذاكرة
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                # فحص مساحة القرص
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                
                health_data = {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'disk_percent': disk_percent,
                    'timestamp': datetime.utcnow().isoformat()
                }
                
                # تحذيرات
                warnings = []
                if cpu_percent > 80:
                    warnings.append(f"استخدام المعالج مرتفع: {cpu_percent}%")
                if memory_percent > 80:
                    warnings.append(f"استخدام الذاكرة مرتفع: {memory_percent}%")
                if disk_percent > 80:
                    warnings.append(f"مساحة القرص منخفضة: {disk_percent}%")
                
                health_data['warnings'] = warnings
                
                if warnings:
                    logger.warning(f"تحذيرات النظام: {', '.join(warnings)}")
                else:
                    logger.info("فحص صحة النظام: كل شيء طبيعي")
                
                return health_data
                
            except ImportError:
                logger.warning("مكتبة psutil غير متاحة لفحص صحة النظام")
                return {'error': 'psutil not available'}
            except Exception as e:
                logger.error(f"خطأ في فحص صحة النظام: {e}")
                raise
        
        # تسجيل المهام
        self.register_task_function('cleanup_old_logs', cleanup_old_logs)
        self.register_task_function('backup_database', backup_database)
        self.register_task_function('system_health_check', system_health_check)
    
    def schedule_task(self, task: Task) -> str:
        """جدولة مهمة"""
        with self.lock:
            self.tasks[task.id] = task
            self._save_task(task)
            self._log_task_event(task.id, 'INFO', f'تم جدولة المهمة: {task.name}')
            
        logger.info(f"تم جدولة المهمة: {task.name} ({task.id})")
        return task.id
    
    def cancel_task(self, task_id: str) -> bool:
        """إلغاء مهمة"""
        with self.lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                if task.status in [TaskStatus.PENDING, TaskStatus.RETRYING]:
                    task.status = TaskStatus.CANCELLED
                    self._save_task(task)
                    self._log_task_event(task_id, 'INFO', 'تم إلغاء المهمة')
                    
                    logger.info(f"تم إلغاء المهمة: {task.name} ({task_id})")
                    return True
                else:
                    logger.warning(f"لا يمكن إلغاء المهمة {task_id} - الحالة: {task.status.value}")
                    return False
            else:
                logger.warning(f"المهمة {task_id} غير موجودة")
                return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """الحصول على حالة المهمة"""
        with self.lock:
            if task_id in self.tasks:
                return self.tasks[task_id].status
            return None
    
    def get_task_info(self, task_id: str) -> Optional[Dict]:
        """الحصول على معلومات المهمة"""
        with self.lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                return asdict(task)
            return None
    
    def list_tasks(self, status: Optional[TaskStatus] = None) -> List[Dict]:
        """قائمة المهام"""
        with self.lock:
            tasks = []
            for task in self.tasks.values():
                if status is None or task.status == status:
                    tasks.append(asdict(task))
            
            # ترتيب حسب الأولوية والوقت
            tasks.sort(key=lambda x: (x['priority'], x['scheduled_time']))
            return tasks
    
    def get_task_logs(self, task_id: str) -> List[Dict]:
        """الحصول على سجلات المهمة"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.execute('''
                    SELECT timestamp, level, message 
                    FROM task_logs 
                    WHERE task_id = ? 
                    ORDER BY timestamp DESC
                ''', (task_id,))
                
                logs = []
                for row in cursor.fetchall():
                    logs.append({
                        'timestamp': row['timestamp'],
                        'level': row['level'],
                        'message': row['message']
                    })
                
                return logs
                
        except Exception as e:
            logger.error(f"خطأ في قراءة سجلات المهمة {task_id}: {e}")
            return []
    
    def start_scheduler(self):
        """بدء جدولة المهام"""
        if self.running:
            logger.warning("الجدولة تعمل بالفعل")
            return
        
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        
        logger.info("تم بدء جدولة المهام")
    
    def stop_scheduler(self):
        """إيقاف جدولة المهام"""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        
        logger.info("تم إيقاف جدولة المهام")
    
    def _worker_loop(self):
        """حلقة العمل الرئيسية"""
        while self.running:
            try:
                # البحث عن المهام الجاهزة للتنفيذ
                ready_tasks = self._get_ready_tasks()
                
                for task in ready_tasks:
                    if not self.running:
                        break
                    
                    # تنفيذ المهمة في thread منفصل
                    task_thread = threading.Thread(
                        target=self._execute_task, 
                        args=(task,), 
                        daemon=True
                    )
                    task_thread.start()
                
                # انتظار قبل الفحص التالي
                time.sleep(10)
                
            except Exception as e:
                logger.error(f"خطأ في حلقة العمل: {e}")
                time.sleep(30)
    
    def _get_ready_tasks(self) -> List[Task]:
        """الحصول على المهام الجاهزة للتنفيذ"""
        ready_tasks = []
        current_time = datetime.utcnow()
        
        with self.lock:
            for task in self.tasks.values():
                if (task.status in [TaskStatus.PENDING, TaskStatus.RETRYING] and 
                    task.scheduled_time <= current_time):
                    ready_tasks.append(task)
        
        # ترتيب حسب الأولوية
        ready_tasks.sort(key=lambda x: x.priority.value, reverse=True)
        
        return ready_tasks
    
    def _execute_task(self, task: Task):
        """تنفيذ المهمة"""
        task_id = task.id
        
        try:
            # تحديث حالة المهمة
            with self.lock:
                task.status = TaskStatus.RUNNING
                task.started_at = datetime.utcnow()
                self._save_task(task)
            
            self._log_task_event(task_id, 'INFO', 'بدء تنفيذ المهمة')
            
            # التحقق من وجود الدالة
            if task.function_name not in self.task_functions:
                raise Exception(f"دالة المهمة غير موجودة: {task.function_name}")
            
            # تنفيذ المهمة مع timeout
            function = self.task_functions[task.function_name]
            
            # تنفيذ المهمة
            result = function(*task.args, **task.kwargs)
            
            # تحديث حالة النجاح
            with self.lock:
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.utcnow()
                task.result = result
                self._save_task(task)
            
            self._log_task_event(task_id, 'INFO', 'تم تنفيذ المهمة بنجاح')
            logger.info(f"تم تنفيذ المهمة بنجاح: {task.name} ({task_id})")
            
        except Exception as e:
            error_message = str(e)
            
            with self.lock:
                task.retry_count += 1
                task.error_message = error_message
                
                if task.retry_count < task.max_retries:
                    # إعادة جدولة المهمة
                    task.status = TaskStatus.RETRYING
                    task.scheduled_time = datetime.utcnow() + timedelta(seconds=task.retry_delay)
                    self._log_task_event(task_id, 'WARNING', 
                                       f'فشل في التنفيذ، إعادة محاولة {task.retry_count}/{task.max_retries}: {error_message}')
                else:
                    # فشل نهائي
                    task.status = TaskStatus.FAILED
                    task.completed_at = datetime.utcnow()
                    self._log_task_event(task_id, 'ERROR', f'فشل نهائي في التنفيذ: {error_message}')
                
                self._save_task(task)
            
            logger.error(f"خطأ في تنفيذ المهمة {task.name} ({task_id}): {error_message}")
    
    def get_statistics(self) -> Dict:
        """إحصائيات المهام"""
        try:
            with self._get_db_connection() as conn:
                # إحصائيات عامة
                cursor = conn.execute('SELECT status, COUNT(*) as count FROM tasks GROUP BY status')
                status_counts = {row['status']: row['count'] for row in cursor.fetchall()}
                
                # المهام الأخيرة
                cursor = conn.execute('''
                    SELECT COUNT(*) as count FROM tasks 
                    WHERE created_at > datetime('now', '-24 hours')
                ''')
                recent_tasks = cursor.fetchone()['count']
                
                # المهام الفاشلة الأخيرة
                cursor = conn.execute('''
                    SELECT COUNT(*) as count FROM tasks 
                    WHERE status = 'failed' AND completed_at > datetime('now', '-24 hours')
                ''')
                recent_failures = cursor.fetchone()['count']
                
                return {
                    'total_tasks': sum(status_counts.values()),
                    'status_counts': status_counts,
                    'recent_tasks_24h': recent_tasks,
                    'recent_failures_24h': recent_failures,
                    'running': self.running,
                    'registered_functions': len(self.task_functions)
                }
                
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {e}")
            return {}

def init_task_manager(app=None):
    """تهيئة مدير المهام"""
    task_manager = TaskManager()
    
    # بدء الجدولة
    task_manager.start_scheduler()
    
    # جدولة المهام الدورية
    from datetime import datetime, timedelta
    import uuid
    
    # تنظيف السجلات القديمة - يومياً في الساعة 2:00 صباحاً
    cleanup_task = Task(
        id=str(uuid.uuid4()),
        name="تنظيف السجلات القديمة",
        description="حذف سجلات المهام الأقدم من 30 يوم",
        function_name="cleanup_old_logs",
        args=[],
        kwargs={},
        priority=TaskPriority.LOW,
        scheduled_time=datetime.utcnow().replace(hour=2, minute=0, second=0) + timedelta(days=1)
    )
    task_manager.schedule_task(cleanup_task)
    
    # فحص صحة النظام - كل ساعة
    health_check_task = Task(
        id=str(uuid.uuid4()),
        name="فحص صحة النظام",
        description="مراقبة استخدام الموارد والتحقق من صحة النظام",
        function_name="system_health_check",
        args=[],
        kwargs={},
        priority=TaskPriority.NORMAL,
        scheduled_time=datetime.utcnow() + timedelta(hours=1)
    )
    task_manager.schedule_task(health_check_task)
    
    logger.info("تم تهيئة نظام إدارة المهام")
    return task_manager