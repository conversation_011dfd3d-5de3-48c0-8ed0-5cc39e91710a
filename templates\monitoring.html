{% extends "base.html" %}

{% block title %}مراقبة النظام{% endblock %}

{% block extra_css %}
<style>
.metric-card {
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.status-healthy { color: #28a745; }
.status-warning { color: #ffc107; }
.status-critical { color: #dc3545; }

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.alert-item {
    border-left: 4px solid;
    margin-bottom: 10px;
}

.alert-critical { border-left-color: #dc3545; }
.alert-warning { border-left-color: #ffc107; }
.alert-info { border-left-color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-chart-line"></i> مراقبة النظام</h2>
        <div>
          <button onclick="refreshData()" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-sync-alt"></i> تحديث
          </button>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right"></i> العودة
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- حالة النظام العامة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body text-center">
          {% if status %}
            {% if status.status == 'healthy' %}
              <i class="fas fa-check-circle fa-3x status-healthy mb-3"></i>
              <h4 class="status-healthy">النظام يعمل بشكل طبيعي</h4>
            {% elif status.status == 'warning' %}
              <i class="fas fa-exclamation-triangle fa-3x status-warning mb-3"></i>
              <h4 class="status-warning">تحذير: يحتاج النظام للمراقبة</h4>
            {% elif status.status == 'critical' %}
              <i class="fas fa-times-circle fa-3x status-critical mb-3"></i>
              <h4 class="status-critical">تنبيه: النظام يحتاج تدخل فوري</h4>
            {% endif %}
            <p class="text-muted">آخر تحديث: {{ moment(status.timestamp).format('YYYY-MM-DD HH:mm:ss') if moment else status.timestamp[:19] }}</p>
          {% else %}
            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">حالة النظام غير معروفة</h4>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- مقاييس النظام الحالية -->
  {% if status %}
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card metric-card">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">المعالج</h6>
              <h4 class="mb-0">{{ "%.1f"|format(status.cpu_percent) }}%</h4>
            </div>
            <div class="text-primary">
              <i class="fas fa-microchip fa-2x"></i>
            </div>
          </div>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar {% if status.cpu_percent > 80 %}bg-danger{% elif status.cpu_percent > 60 %}bg-warning{% else %}bg-success{% endif %}" 
                 style="width: {{ status.cpu_percent }}%"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card metric-card">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">الذاكرة</h6>
              <h4 class="mb-0">{{ "%.1f"|format(status.memory_percent) }}%</h4>
            </div>
            <div class="text-info">
              <i class="fas fa-memory fa-2x"></i>
            </div>
          </div>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar {% if status.memory_percent > 85 %}bg-danger{% elif status.memory_percent > 70 %}bg-warning{% else %}bg-info{% endif %}" 
                 style="width: {{ status.memory_percent }}%"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card metric-card">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">القرص الصلب</h6>
              <h4 class="mb-0">{{ "%.1f"|format(status.disk_percent) }}%</h4>
            </div>
            <div class="text-warning">
              <i class="fas fa-hdd fa-2x"></i>
            </div>
          </div>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar {% if status.disk_percent > 90 %}bg-danger{% elif status.disk_percent > 75 %}bg-warning{% else %}bg-success{% endif %}" 
                 style="width: {{ status.disk_percent }}%"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card metric-card">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">وقت الاستجابة</h6>
              <h4 class="mb-0">{{ "%.2f"|format(status.response_time) }}s</h4>
            </div>
            <div class="text-success">
              <i class="fas fa-tachometer-alt fa-2x"></i>
            </div>
          </div>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar {% if status.response_time > 5 %}bg-danger{% elif status.response_time > 2 %}bg-warning{% else %}bg-success{% endif %}" 
                 style="width: {{ (status.response_time / 10 * 100)|min(100) }}%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- الرسوم البيانية -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-chart-line"></i> استخدام المعالج والذاكرة</h6>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas id="cpuMemoryChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-clock"></i> أوقات الاستجابة</h6>
        </div>
        <div class="card-body">
          <div class="chart-container">
            <canvas id="responseTimeChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- التقرير والإحصائيات -->
  {% if report %}
  <div class="row mb-4">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-chart-bar"></i> إحصائيات آخر 24 ساعة</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="text-center">
                <h5 class="text-primary">{{ "%.1f"|format(report.averages_24h.cpu_percent) }}%</h5>
                <small class="text-muted">متوسط استخدام المعالج</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="text-center">
                <h5 class="text-info">{{ "%.1f"|format(report.averages_24h.memory_percent) }}%</h5>
                <small class="text-muted">متوسط استخدام الذاكرة</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="text-center">
                <h5 class="text-success">{{ "%.2f"|format(report.averages_24h.response_time) }}s</h5>
                <small class="text-muted">متوسط وقت الاستجابة</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> التنبيهات</h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-6">
              <h4 class="text-danger">{{ report.critical_alerts }}</h4>
              <small class="text-muted">حرجة</small>
            </div>
            <div class="col-6">
              <h4 class="text-warning">{{ report.warning_alerts }}</h4>
              <small class="text-muted">تحذيرية</small>
            </div>
          </div>
          <hr>
          <div class="text-center">
            <h5>{{ report.alerts_count_24h }}</h5>
            <small class="text-muted">إجمالي التنبيهات</small>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- التنبيهات الحديثة -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-bell"></i> التنبيهات الحديثة</h6>
        </div>
        <div class="card-body">
          <div id="alertsList">
            <div class="text-center py-3">
              <i class="fas fa-spinner fa-spin"></i> جاري تحميل التنبيهات...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let cpuMemoryChart, responseTimeChart;

// تحديث البيانات
function refreshData() {
  location.reload();
}

// تحميل التنبيهات
function loadAlerts() {
  fetch('/api/system/alerts?hours=24')
    .then(response => response.json())
    .then(alerts => {
      const alertsList = document.getElementById('alertsList');
      
      if (alerts.length === 0) {
        alertsList.innerHTML = `
          <div class="text-center py-3">
            <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
            <p class="text-muted">لا توجد تنبيهات</p>
          </div>
        `;
        return;
      }
      
      let html = '';
      alerts.slice(0, 10).forEach(alert => {
        const alertClass = alert.severity === 'critical' ? 'alert-critical' : 
                          alert.severity === 'warning' ? 'alert-warning' : 'alert-info';
        
        const icon = alert.severity === 'critical' ? 'fas fa-times-circle text-danger' :
                    alert.severity === 'warning' ? 'fas fa-exclamation-triangle text-warning' :
                    'fas fa-info-circle text-info';
        
        html += `
          <div class="alert-item ${alertClass} p-3 mb-2 bg-light rounded">
            <div class="d-flex align-items-center">
              <i class="${icon} me-3"></i>
              <div class="flex-grow-1">
                <div class="fw-bold">${alert.message}</div>
                <small class="text-muted">
                  ${moment(alert.timestamp).format('YYYY-MM-DD HH:mm:ss')}
                </small>
              </div>
              <div class="text-end">
                <small class="text-muted">
                  القيمة: ${alert.value?.toFixed(2) || 'N/A'}
                  ${alert.threshold ? `/ الحد: ${alert.threshold}` : ''}
                </small>
              </div>
            </div>
          </div>
        `;
      });
      
      alertsList.innerHTML = html;
    })
    .catch(error => {
      console.error('خطأ في تحميل التنبيهات:', error);
      document.getElementById('alertsList').innerHTML = `
        <div class="text-center py-3 text-danger">
          <i class="fas fa-exclamation-circle"></i> خطأ في تحميل التنبيهات
        </div>
      `;
    });
}

// تحميل بيانات المقاييس وإنشاء الرسوم البيانية
function loadMetricsAndCharts() {
  fetch('/api/system/metrics?hours=6')
    .then(response => response.json())
    .then(metrics => {
      if (metrics.length === 0) return;
      
      // تحضير البيانات للرسوم البيانية
      const labels = metrics.reverse().map(m => 
        moment(m.timestamp).format('HH:mm')
      );
      
      const cpuData = metrics.map(m => m.cpu_percent);
      const memoryData = metrics.map(m => m.memory_percent);
      const responseData = metrics.map(m => m.response_time);
      
      // رسم بياني للمعالج والذاكرة
      const cpuMemoryCtx = document.getElementById('cpuMemoryChart').getContext('2d');
      if (cpuMemoryChart) cpuMemoryChart.destroy();
      
      cpuMemoryChart = new Chart(cpuMemoryCtx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: 'المعالج (%)',
            data: cpuData,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
          }, {
            label: 'الذاكرة (%)',
            data: memoryData,
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: 100
            }
          }
        }
      });
      
      // رسم بياني لأوقات الاستجابة
      const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
      if (responseTimeChart) responseTimeChart.destroy();
      
      responseTimeChart = new Chart(responseCtx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: 'وقت الاستجابة (ثانية)',
            data: responseData,
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
    })
    .catch(error => {
      console.error('خطأ في تحميل المقاييس:', error);
    });
}

// تحديث تلقائي كل دقيقة
setInterval(() => {
  loadAlerts();
  loadMetricsAndCharts();
}, 60000);

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  loadAlerts();
  loadMetricsAndCharts();
});
</script>
{% endblock %}