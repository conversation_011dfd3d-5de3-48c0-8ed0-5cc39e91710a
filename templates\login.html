<!-- templates/login.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>تسجيل الدخول</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body class="bg-light">
  <div class="container d-flex align-items-center justify-content-center min-vh-100">
    <div class="card shadow-sm" style="max-width: 420px; width: 100%;">
      <div class="card-body p-4">
        <h3 class="mb-3 text-center">تسجيل الدخول</h3>

        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            <div class="mb-3">
              {% for category, message in messages %}
                <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
              {% endfor %}
            </div>
          {% endif %}
        {% endwith %}

        <form method="post" novalidate>
          <div class="mb-3">
            <label class="form-label">اسم المستخدم</label>
            <input type="text" name="username" class="form-control" placeholder="admin" required>
          </div>
          <div class="mb-3">
            <label class="form-label">كلمة المرور</label>
            <input type="password" name="password" class="form-control" placeholder="••••••••" required>
          </div>
          <button type="submit" class="btn btn-primary w-100">دخول</button>
        </form>
      </div>
    </div>
  </div>
</body>
</html>