<!-- templates/payments.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>إدارة المدفوعات</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <div class="dropdown me-2">
        <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
          إدارة
        </button>
        <ul class="dropdown-menu">
          <li><a class="dropdown-item" href="{{ url_for('dashboard') }}">السائقون</a></li>
          <li><a class="dropdown-item" href="{{ url_for('vehicles') }}">المركبات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('payments') }}">المدفوعات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('salaries') }}">الرواتب</a></li>
          <li><a class="dropdown-item" href="{{ url_for('expenses') }}">المصروفات</a></li>
        </ul>
      </div>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light me-2">الإعدادات</a>
      <a href="{{ url_for('logout') }}" class="btn btn-outline-light">خروج</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <!-- إحصائيات سريعة -->
  {% if filters.driver_id or filters.direction or filters.method or filters.date_from or filters.date_to %}
  <div class="row g-3 mb-4">
    <div class="col-md-6">
      <div class="card text-center shadow-sm bg-success text-white">
        <div class="card-body">
          <div class="h4 mb-1">{{ "%.2f"|format(total_in) }}</div>
          <div>إجمالي الداخل</div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card text-center shadow-sm bg-danger text-white">
        <div class="card-body">
          <div class="h4 mb-1">{{ "%.2f"|format(total_out) }}</div>
          <div>إجمالي الخارج</div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- فلاتر البحث -->
  <div class="card shadow-sm mb-4">
    <div class="card-body">
      <h5 class="mb-3">فلترة المدفوعات</h5>
      <form method="get" class="row g-3">
        <div class="col-md-3">
          <label class="form-label">السائق</label>
          <select name="driver_id" class="form-select">
            <option value="">جميع السائقين</option>
            {% for driver in drivers %}
              <option value="{{ driver.id }}" {% if filters.driver_id and filters.driver_id|int == driver.id %}selected{% endif %}>
                {{ driver.name }}
              </option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">النوع</label>
          <select name="direction" class="form-select">
            <option value="">الكل</option>
            <option value="in" {% if filters.direction == 'in' %}selected{% endif %}>دخل</option>
            <option value="out" {% if filters.direction == 'out' %}selected{% endif %}>خرج</option>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">الطريقة</label>
          <select name="method" class="form-select">
            <option value="">الكل</option>
            <option value="نقد" {% if filters.method == 'نقد' %}selected{% endif %}>نقد</option>
            <option value="تحويل" {% if filters.method == 'تحويل' %}selected{% endif %}>تحويل</option>
            <option value="شيك" {% if filters.method == 'شيك' %}selected{% endif %}>شيك</option>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">من تاريخ</label>
          <input type="date" name="date_from" class="form-control" value="{{ filters.date_from or '' }}">
        </div>
        <div class="col-md-2">
          <label class="form-label">إلى تاريخ</label>
          <input type="date" name="date_to" class="form-control" value="{{ filters.date_to or '' }}">
        </div>
        <div class="col-md-1 d-flex align-items-end">
          <button type="submit" class="btn btn-primary w-100">فلترة</button>
        </div>
      </form>
      {% if filters.driver_id or filters.direction or filters.method or filters.date_from or filters.date_to %}
        <div class="mt-2">
          <a href="{{ url_for('payments') }}" class="btn btn-outline-secondary btn-sm">إلغاء الفلاتر</a>
        </div>
      {% endif %}
    </div>
  </div>

  <div class="d-flex justify-content-between align-items-center mb-3">
    <h4>إدارة المدفوعات</h4>
    <a href="{{ url_for('add_payment') }}" class="btn btn-primary">إضافة مدفوعة</a>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped align-middle">
          <thead>
            <tr>
              <th>#</th>
              <th>السائق</th>
              <th>المبلغ</th>
              <th>النوع</th>
              <th>الطريقة</th>
              <th>التاريخ</th>
              <th>ملاحظة</th>
              <th class="text-center">إجراءات</th>
            </tr>
          </thead>
          <tbody>
            {% for p in payments %}
              <tr>
                <td>{{ p.id }}</td>
                <td>
                  <a href="{{ url_for('driver_detail', driver_id=p.driver.id) }}">{{ p.driver.name }}</a>
                </td>
                <td>
                  {% if p.direction == 'in' %}
                    <span class="text-success">+{{ "%.2f"|format(p.amount) }}</span>
                  {% else %}
                    <span class="text-danger">-{{ "%.2f"|format(p.amount) }}</span>
                  {% endif %}
                </td>
                <td>
                  {% if p.direction == 'in' %}
                    <span class="badge bg-success">دخل</span>
                  {% else %}
                    <span class="badge bg-danger">خرج</span>
                  {% endif %}
                </td>
                <td>{{ p.method or '-' }}</td>
                <td>{{ p.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                <td>{{ p.note or '-' }}</td>
                <td class="text-center">
                  <form action="{{ url_for('delete_payment', payment_id=p.id) }}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('تأكيد حذف المدفوعة؟ سيتم تعديل رصيد السائق.');">حذف</button>
                  </form>
                </td>
              </tr>
            {% else %}
              <tr>
                <td colspan="8" class="text-center text-muted">لا توجد مدفوعات حتى الآن.</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- الترقيم -->
  {% if pagination.pages > 1 %}
  <nav aria-label="ترقيم الصفحات" class="mt-4">
    <ul class="pagination justify-content-center">
      {% if pagination.has_prev %}
        <li class="page-item">
          <a class="page-link" href="{{ url_for('payments', page=pagination.prev_num, **filters) }}">السابق</a>
        </li>
      {% endif %}
      
      {% for page_num in pagination.iter_pages() %}
        {% if page_num %}
          {% if page_num != pagination.page %}
            <li class="page-item">
              <a class="page-link" href="{{ url_for('payments', page=page_num, **filters) }}">{{ page_num }}</a>
            </li>
          {% else %}
            <li class="page-item active">
              <span class="page-link">{{ page_num }}</span>
            </li>
          {% endif %}
        {% else %}
          <li class="page-item disabled">
            <span class="page-link">...</span>
          </li>
        {% endif %}
      {% endfor %}
      
      {% if pagination.has_next %}
        <li class="page-item">
          <a class="page-link" href="{{ url_for('payments', page=pagination.next_num, **filters) }}">التالي</a>
        </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>