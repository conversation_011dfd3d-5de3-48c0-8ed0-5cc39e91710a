<!-- templates/edit_driver.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>تعديل سائق</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <a href="{{ url_for('dashboard') }}" class="btn btn-light">عودة</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="card shadow-sm">
    <div class="card-body">
      <h4 class="mb-3">تعديل بيانات السائق</h4>
      <form method="post" novalidate>
        <div class="row g-3">
          <div class="col-md-4">
            <label class="form-label">الاسم</label>
            <input type="text" name="name" class="form-control" value="{{ driver.name }}" required>
          </div>
          <div class="col-md-4">
            <label class="form-label">رقم الهاتف</label>
            <input type="text" name="phone" class="form-control" value="{{ driver.phone }}" required>
          </div>
          <div class="col-md-4">
            <label class="form-label">الرصيد</label>
            <input type="number" step="0.01" name="balance" class="form-control" value="{{ driver.balance }}">
          </div>
        </div>
        <div class="mt-3">
          <button type="submit" class="btn btn-primary">تحديث</button>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">إلغاء</a>
        </div>
      </form>
    </div>
  </div>
</div>
</body>
</html>