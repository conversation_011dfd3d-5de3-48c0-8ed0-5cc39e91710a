#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطية المتقدم
Advanced Backup Manager

يوفر:
- نسخ احتياطية تلقائية ويدوية
- ضغط البيانات
- تشفير النسخ الاحتياطية
- استعادة البيانات
- تنظيف النسخ القديمة
- رفع النسخ للسحابة
"""

import os
import shutil
import sqlite3
import zipfile
import json
import hashlib
import logging
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import schedule
import time
from threading import Thread

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self, app=None):
        self.app = app
        self.backup_dir = 'backups'
        self.temp_dir = 'temp_backup'
        self.encryption_key = None
        
        # إنشاء المجلدات المطلوبة
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # تحميل أو إنشاء مفتاح التشفير
        self.load_or_create_encryption_key()
        
        # إعدادات النسخ الاحتياطية
        self.config = {
            'max_backups': 30,  # أقصى عدد نسخ احتياطية
            'compress': True,   # ضغط النسخ
            'encrypt': True,    # تشفير النسخ
            'include_logs': False,  # تضمين ملفات السجلات
            'cloud_upload': False   # رفع للسحابة
        }
        
        self.load_config()
    
    def load_or_create_encryption_key(self):
        """تحميل أو إنشاء مفتاح التشفير"""
        key_file = os.path.join(self.backup_dir, '.backup_key')
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                self.encryption_key = f.read()
        else:
            self.encryption_key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(self.encryption_key)
            
            # إخفاء الملف في Windows
            if os.name == 'nt':
                os.system(f'attrib +h "{key_file}"')
        
        logger.info("تم تحميل مفتاح التشفير")
    
    def load_config(self):
        """تحميل إعدادات النسخ الاحتياطية"""
        config_file = os.path.join(self.backup_dir, 'backup_config.json')
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                logger.info("تم تحميل إعدادات النسخ الاحتياطية")
            except Exception as e:
                logger.error(f"خطأ في تحميل إعدادات النسخ الاحتياطية: {e}")
    
    def save_config(self):
        """حفظ إعدادات النسخ الاحتياطية"""
        config_file = os.path.join(self.backup_dir, 'backup_config.json')
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info("تم حفظ إعدادات النسخ الاحتياطية")
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات النسخ الاحتياطية: {e}")
    
    def create_backup(self, backup_type='manual', description=''):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f'{backup_type}_backup_{timestamp}'
            
            logger.info(f"بدء إنشاء النسخة الاحتياطية: {backup_name}")
            
            # إنشاء مجلد مؤقت للنسخة الاحتياطية
            temp_backup_path = os.path.join(self.temp_dir, backup_name)
            os.makedirs(temp_backup_path, exist_ok=True)
            
            # نسخ قاعدة البيانات
            db_files = ['app.db', 'drivers.db']  # قواعد البيانات المحتملة
            
            for db_file in db_files:
                if os.path.exists(db_file):
                    shutil.copy2(db_file, temp_backup_path)
                    logger.info(f"تم نسخ قاعدة البيانات: {db_file}")
            
            # نسخ ملفات التكوين
            config_files = ['config.py', '.env', 'requirements.txt']
            for config_file in config_files:
                if os.path.exists(config_file):
                    shutil.copy2(config_file, temp_backup_path)
            
            # نسخ مجلد الإشعارات إذا كان موجوداً
            if os.path.exists('notifications'):
                shutil.copytree('notifications', 
                              os.path.join(temp_backup_path, 'notifications'),
                              dirs_exist_ok=True)
            
            # نسخ ملفات السجلات إذا كان مطلوباً
            if self.config['include_logs'] and os.path.exists('logs'):
                shutil.copytree('logs', 
                              os.path.join(temp_backup_path, 'logs'),
                              dirs_exist_ok=True)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                'name': backup_name,
                'type': backup_type,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'files_count': len(os.listdir(temp_backup_path)),
                'version': '1.0.0'
            }
            
            with open(os.path.join(temp_backup_path, 'backup_info.json'), 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            # ضغط النسخة الاحتياطية
            if self.config['compress']:
                backup_file = self.compress_backup(temp_backup_path, backup_name)
            else:
                backup_file = os.path.join(self.backup_dir, backup_name)
                shutil.move(temp_backup_path, backup_file)
            
            # تشفير النسخة الاحتياطية
            if self.config['encrypt']:
                backup_file = self.encrypt_backup(backup_file)
            
            # حساب checksum
            checksum = self.calculate_checksum(backup_file)
            backup_info['checksum'] = checksum
            backup_info['encrypted'] = self.config['encrypt']
            backup_info['compressed'] = self.config['compress']
            
            # حفظ معلومات النسخة الاحتياطية
            info_file = backup_file + '.info'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            # تنظيف المجلد المؤقت
            if os.path.exists(temp_backup_path):
                shutil.rmtree(temp_backup_path)
            
            # رفع للسحابة إذا كان مطلوباً
            if self.config['cloud_upload']:
                self.upload_to_cloud(backup_file)
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            logger.info(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_file}")
            
            return {
                'success': True,
                'backup_file': backup_file,
                'backup_info': backup_info
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            
            # تنظيف في حالة الخطأ
            if os.path.exists(temp_backup_path):
                shutil.rmtree(temp_backup_path)
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def compress_backup(self, backup_path, backup_name):
        """ضغط النسخة الاحتياطية"""
        zip_file = os.path.join(self.backup_dir, f'{backup_name}.zip')
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, backup_path)
                    zipf.write(file_path, arc_name)
        
        logger.info(f"تم ضغط النسخة الاحتياطية: {zip_file}")
        return zip_file
    
    def encrypt_backup(self, backup_file):
        """تشفير النسخة الاحتياطية"""
        encrypted_file = backup_file + '.encrypted'
        
        fernet = Fernet(self.encryption_key)
        
        with open(backup_file, 'rb') as f:
            data = f.read()
        
        encrypted_data = fernet.encrypt(data)
        
        with open(encrypted_file, 'wb') as f:
            f.write(encrypted_data)
        
        # حذف الملف غير المشفر
        os.remove(backup_file)
        
        logger.info(f"تم تشفير النسخة الاحتياطية: {encrypted_file}")
        return encrypted_file
    
    def decrypt_backup(self, encrypted_file):
        """فك تشفير النسخة الاحتياطية"""
        decrypted_file = encrypted_file.replace('.encrypted', '')
        
        fernet = Fernet(self.encryption_key)
        
        with open(encrypted_file, 'rb') as f:
            encrypted_data = f.read()
        
        decrypted_data = fernet.decrypt(encrypted_data)
        
        with open(decrypted_file, 'wb') as f:
            f.write(decrypted_data)
        
        logger.info(f"تم فك تشفير النسخة الاحتياطية: {decrypted_file}")
        return decrypted_file
    
    def calculate_checksum(self, file_path):
        """حساب checksum للملف"""
        hash_md5 = hashlib.md5()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest()
    
    def verify_backup(self, backup_file):
        """التحقق من سلامة النسخة الاحتياطية"""
        info_file = backup_file + '.info'
        
        if not os.path.exists(info_file):
            return {'valid': False, 'error': 'ملف المعلومات غير موجود'}
        
        try:
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            # التحقق من checksum
            current_checksum = self.calculate_checksum(backup_file)
            stored_checksum = backup_info.get('checksum')
            
            if current_checksum != stored_checksum:
                return {'valid': False, 'error': 'checksum غير متطابق'}
            
            return {'valid': True, 'info': backup_info}
            
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def list_backups(self):
        """قائمة النسخ الاحتياطية"""
        backups = []
        
        for file in os.listdir(self.backup_dir):
            if file.endswith('.info'):
                info_file = os.path.join(self.backup_dir, file)
                backup_file = info_file.replace('.info', '')
                
                if os.path.exists(backup_file):
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                        
                        backup_info['file_path'] = backup_file
                        backup_info['file_size'] = os.path.getsize(backup_file)
                        
                        backups.append(backup_info)
                        
                    except Exception as e:
                        logger.error(f"خطأ في قراءة معلومات النسخة الاحتياطية {file}: {e}")
        
        # ترتيب حسب تاريخ الإنشاء
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        return backups
    
    def restore_backup(self, backup_file, restore_path='.'):
        """استعادة النسخة الاحتياطية"""
        try:
            logger.info(f"بدء استعادة النسخة الاحتياطية: {backup_file}")
            
            # التحقق من سلامة النسخة الاحتياطية
            verification = self.verify_backup(backup_file)
            if not verification['valid']:
                raise Exception(f"النسخة الاحتياطية غير صالحة: {verification['error']}")
            
            backup_info = verification['info']
            
            # فك التشفير إذا كان مطلوباً
            if backup_info.get('encrypted', False):
                backup_file = self.decrypt_backup(backup_file)
            
            # فك الضغط إذا كان مطلوباً
            if backup_info.get('compressed', False):
                with zipfile.ZipFile(backup_file, 'r') as zipf:
                    zipf.extractall(restore_path)
            else:
                # نسخ الملفات مباشرة
                if os.path.isdir(backup_file):
                    for item in os.listdir(backup_file):
                        src = os.path.join(backup_file, item)
                        dst = os.path.join(restore_path, item)
                        
                        if os.path.isdir(src):
                            shutil.copytree(src, dst, dirs_exist_ok=True)
                        else:
                            shutil.copy2(src, dst)
            
            logger.info("تم استعادة النسخة الاحتياطية بنجاح")
            
            return {
                'success': True,
                'message': 'تم استعادة النسخة الاحتياطية بنجاح',
                'backup_info': backup_info
            }
            
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            if len(backups) > self.config['max_backups']:
                # حذف النسخ الزائدة (الأقدم)
                backups_to_delete = backups[self.config['max_backups']:]
                
                for backup in backups_to_delete:
                    backup_file = backup['file_path']
                    info_file = backup_file + '.info'
                    
                    if os.path.exists(backup_file):
                        os.remove(backup_file)
                    
                    if os.path.exists(info_file):
                        os.remove(info_file)
                    
                    logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
                
                logger.info(f"تم حذف {len(backups_to_delete)} نسخة احتياطية قديمة")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def upload_to_cloud(self, backup_file):
        """رفع النسخة الاحتياطية للسحابة"""
        # يمكن تطبيق هذه الوظيفة مع خدمات السحابة مثل:
        # - Google Drive
        # - Dropbox
        # - AWS S3
        # - Azure Blob Storage
        
        logger.info(f"رفع النسخة الاحتياطية للسحابة: {backup_file}")
        # TODO: تطبيق رفع السحابة
        pass
    
    def schedule_automatic_backups(self):
        """جدولة النسخ الاحتياطية التلقائية"""
        # نسخة احتياطية يومية في الساعة 2:00 صباحاً
        schedule.every().day.at("02:00").do(
            lambda: self.create_backup('auto_daily', 'نسخة احتياطية يومية تلقائية')
        )
        
        # نسخة احتياطية أسبوعية يوم الأحد في الساعة 1:00 صباحاً
        schedule.every().sunday.at("01:00").do(
            lambda: self.create_backup('auto_weekly', 'نسخة احتياطية أسبوعية تلقائية')
        )
        
        logger.info("تم جدولة النسخ الاحتياطية التلقائية")
    
    def run_scheduler(self):
        """تشغيل مجدول النسخ الاحتياطية"""
        self.schedule_automatic_backups()
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

# إنشاء مثيل مدير النسخ الاحتياطية
backup_manager = BackupManager()

def init_backup_manager(app):
    """تهيئة مدير النسخ الاحتياطية"""
    backup_manager.app = app
    return backup_manager