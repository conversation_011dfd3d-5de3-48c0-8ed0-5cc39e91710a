{% extends "base.html" %}

{% block title %}تعديل المستخدم - {{ user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-user-edit"></i> تعديل المستخدم: {{ user.username }}</h2>
        <a href="{{ url_for('user_management.users_list') }}" class="btn btn-secondary">
          <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
      </div>
    </div>
  </div>

  <!-- نموذج تعديل المستخدم -->
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-user"></i> تعديل بيانات المستخدم</h6>
        </div>
        <div class="card-body">
          <form id="editUserForm" method="POST">
            <div class="row">
              <!-- اسم المستخدم -->
              <div class="col-md-6 mb-3">
                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="username" name="username" 
                       value="{{ user.username }}" required
                       {{ 'readonly' if user.username == 'admin' else '' }}>
                {% if user.username == 'admin' %}
                  <div class="form-text text-warning">لا يمكن تغيير اسم المدير الرئيسي</div>
                {% endif %}
              </div>

              <!-- الاسم الكامل -->
              <div class="col-md-6 mb-3">
                <label for="full_name" class="form-label">الاسم الكامل</label>
                <input type="text" class="form-control" id="full_name" name="full_name" 
                       value="{{ user.full_name or '' }}">
              </div>
            </div>

            <div class="row">
              <!-- البريد الإلكتروني -->
              <div class="col-md-6 mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="{{ user.email or '' }}">
              </div>

              <!-- الدور -->
              <div class="col-md-6 mb-3">
                <label for="role_name" class="form-label">الدور <span class="text-danger">*</span></label>
                <select class="form-select" id="role_name" name="role_name" required
                        {{ 'disabled' if user.username == 'admin' else '' }}>
                  {% for role in roles %}
                    <option value="{{ role.name }}" 
                            {{ 'selected' if user.role and user.role.name == role.name else '' }}>
                      {{ role.display_name or role.name }}
                    </option>
                  {% endfor %}
                </select>
                {% if user.username == 'admin' %}
                  <div class="form-text text-warning">لا يمكن تغيير دور المدير الرئيسي</div>
                {% endif %}
              </div>
            </div>

            <!-- حالة المستخدم -->
            <div class="row">
              <div class="col-md-6 mb-3">
                <div class="form-check form-switch">
                  <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                         {{ 'checked' if user.is_active else '' }}
                         {{ 'disabled' if user.username == 'admin' else '' }}>
                  <label class="form-check-label" for="is_active">
                    المستخدم نشط
                  </label>
                  {% if user.username == 'admin' %}
                    <div class="form-text text-warning">لا يمكن تعطيل المدير الرئيسي</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- تغيير كلمة المرور -->
            <div class="row">
              <div class="col-12 mb-3">
                <div class="card bg-light">
                  <div class="card-header">
                    <h6 class="mb-0">
                      <i class="fas fa-key"></i> تغيير كلمة المرور
                      <small class="text-muted">(اختياري)</small>
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <!-- كلمة المرور الجديدة -->
                      <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">كلمة المرور الجديدة</label>
                        <div class="input-group">
                          <input type="password" class="form-control" id="password" name="password">
                          <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                            <i class="fas fa-eye" id="password-icon"></i>
                          </button>
                        </div>
                        <div class="form-text">اتركها فارغة إذا كنت لا تريد تغييرها</div>
                      </div>

                      <!-- تأكيد كلمة المرور -->
                      <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <div class="input-group">
                          <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                          <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                            <i class="fas fa-eye" id="confirm_password-icon"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row">
              <div class="col-12 mb-3">
                <div class="card bg-info bg-opacity-10">
                  <div class="card-body">
                    <h6><i class="fas fa-info-circle"></i> معلومات المستخدم</h6>
                    <div class="row">
                      <div class="col-md-4">
                        <small class="text-muted">تاريخ الإنشاء:</small><br>
                        <span>{{ moment(user.created_at).format('YYYY-MM-DD HH:mm') if moment and user.created_at else (user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد') }}</span>
                      </div>
                      <div class="col-md-4">
                        <small class="text-muted">آخر تحديث:</small><br>
                        <span>{{ moment(user.updated_at).format('YYYY-MM-DD HH:mm') if moment and user.updated_at else (user.updated_at.strftime('%Y-%m-%d %H:%M') if user.updated_at else 'لم يتم التحديث') }}</span>
                      </div>
                      <div class="col-md-4">
                        <small class="text-muted">آخر دخول:</small><br>
                        <span>{{ moment(user.last_login).format('YYYY-MM-DD HH:mm') if moment and user.last_login else (user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يسجل دخول') }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- معلومات الدور المختار -->
            <div id="roleInfo" class="alert alert-info">
              <h6><i class="fas fa-info-circle"></i> معلومات الدور</h6>
              <p id="roleDescription"></p>
              <div id="rolePermissions"></div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="row">
              <div class="col-12">
                <hr>
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save"></i> حفظ التغييرات
                </button>
                <button type="reset" class="btn btn-outline-secondary">
                  <i class="fas fa-undo"></i> إعادة تعيين
                </button>
                <a href="{{ url_for('user_management.users_list') }}" class="btn btn-secondary">
                  <i class="fas fa-times"></i> إلغاء
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// معلومات الأدوار
const rolesInfo = {
  {% for role in roles %}
  '{{ role.name }}': {
    name: '{{ role.display_name or role.name }}',
    description: '{{ role.description or "بدون وصف" }}',
    permissions: {{ role.permissions|safe if role.permissions else '[]' }}
  }{% if not loop.last %},{% endif %}
  {% endfor %}
};

// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
  const field = document.getElementById(fieldId);
  const icon = document.getElementById(fieldId + '-icon');
  
  if (field.type === 'password') {
    field.type = 'text';
    icon.classList.remove('fa-eye');
    icon.classList.add('fa-eye-slash');
  } else {
    field.type = 'password';
    icon.classList.remove('fa-eye-slash');
    icon.classList.add('fa-eye');
  }
}

// عرض معلومات الدور عند التغيير
function updateRoleInfo() {
  const roleName = document.getElementById('role_name').value;
  const roleInfo = document.getElementById('roleInfo');
  
  if (roleName && rolesInfo[roleName]) {
    const role = rolesInfo[roleName];
    
    document.getElementById('roleDescription').textContent = role.description;
    
    // عرض الصلاحيات
    const permissionsDiv = document.getElementById('rolePermissions');
    if (role.permissions && role.permissions.length > 0) {
      let permissionsHtml = '<strong>الصلاحيات:</strong><br>';
      permissionsHtml += '<div class="row">';
      
      role.permissions.forEach((permission, index) => {
        if (index % 3 === 0 && index > 0) {
          permissionsHtml += '</div><div class="row">';
        }
        permissionsHtml += `<div class="col-md-4"><small class="badge bg-secondary me-1">${permission}</small></div>`;
      });
      
      permissionsHtml += '</div>';
      permissionsDiv.innerHTML = permissionsHtml;
    } else {
      permissionsDiv.innerHTML = '<small class="text-muted">لا توجد صلاحيات محددة</small>';
    }
    
    roleInfo.style.display = 'block';
  } else {
    roleInfo.style.display = 'none';
  }
}

// تحديث معلومات الدور عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  updateRoleInfo();
});

// تحديث معلومات الدور عند التغيير
document.getElementById('role_name').addEventListener('change', updateRoleInfo);

// التحقق من صحة النموذج
document.getElementById('editUserForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const password = formData.get('password');
  const confirmPassword = formData.get('confirm_password');
  
  // التحقق من كلمة المرور إذا تم إدخالها
  if (password || confirmPassword) {
    if (password.length < 6) {
      alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      return;
    }
    
    if (password !== confirmPassword) {
      alert('كلمة المرور وتأكيدها غير متطابقين');
      return;
    }
  }
  
  // إرسال البيانات
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
  submitBtn.disabled = true;
  
  // تحويل البيانات إلى JSON
  const data = {};
  for (let [key, value] of formData.entries()) {
    if (key === 'is_active') {
      data[key] = true;
    } else if (value) {
      data[key] = value;
    }
  }
  
  // إضافة is_active إذا لم يكن محدد
  if (!formData.has('is_active')) {
    data['is_active'] = false;
  }
  
  fetch(this.action, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => {
    if (response.redirected) {
      window.location.href = response.url;
    } else {
      return response.json();
    }
  })
  .then(data => {
    if (data && data.success) {
      alert('تم تحديث المستخدم بنجاح');
      window.location.href = "{{ url_for('user_management.users_list') }}";
    } else if (data && data.error) {
      alert('خطأ: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في تحديث المستخدم');
  })
  .finally(() => {
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

// التحقق من اسم المستخدم أثناء الكتابة
document.getElementById('username').addEventListener('blur', function() {
  const username = this.value.trim();
  
  if (username && !this.readOnly) {
    // التحقق من عدم وجود مسافات
    if (username.includes(' ')) {
      this.setCustomValidity('اسم المستخدم لا يجب أن يحتوي على مسافات');
      this.reportValidity();
    } else {
      this.setCustomValidity('');
    }
  }
});

// التحقق من البريد الإلكتروني
document.getElementById('email').addEventListener('blur', function() {
  const email = this.value.trim();
  
  if (email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      this.setCustomValidity('البريد الإلكتروني غير صحيح');
      this.reportValidity();
    } else {
      this.setCustomValidity('');
    }
  }
});
</script>
{% endblock %}