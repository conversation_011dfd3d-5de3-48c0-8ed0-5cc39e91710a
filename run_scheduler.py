#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مجدول المهام كخدمة منفصلة
Run Task Scheduler as Separate Service

يمكن تشغيل هذا الملف كعملية منفصلة لتنفيذ المهام المجدولة
"""

import sys
import os
import signal
import threading
import time
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from scheduler import TaskScheduler

class SchedulerService:
    """خدمة مجدول المهام"""
    
    def __init__(self):
        self.scheduler = TaskScheduler()
        self.running = False
        self.thread = None
        
        # إعداد معالجات الإشارات
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """معالج إشارات الإيقاف"""
        print(f"\nتم استلام إشارة الإيقاف ({signum})")
        self.stop()
    
    def start(self):
        """بدء الخدمة"""
        if self.running:
            print("الخدمة تعمل بالفعل")
            return
        
        print("=" * 60)
        print("🕐 خدمة مجدول المهام - نظام إدارة السائقين")
        print("=" * 60)
        print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🔄 حالة الخدمة: بدء التشغيل...")
        print()
        
        self.running = True
        
        # تشغيل المجدول في thread منفصل
        self.thread = threading.Thread(target=self.scheduler.run, daemon=True)
        self.thread.start()
        
        print("✅ تم بدء خدمة مجدول المهام بنجاح")
        print("💡 للإيقاف اضغط Ctrl+C")
        print("=" * 60)
        print()
        
        # انتظار إيقاف الخدمة
        try:
            while self.running and self.thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """إيقاف الخدمة"""
        if not self.running:
            return
        
        print("\n🛑 إيقاف خدمة مجدول المهام...")
        
        self.running = False
        self.scheduler.stop()
        
        # انتظار انتهاء الـ thread
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        print("✅ تم إيقاف خدمة مجدول المهام بنجاح")
        print(f"⏰ وقت الإيقاف: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def status(self):
        """عرض حالة الخدمة"""
        if self.running and self.thread and self.thread.is_alive():
            print("🟢 الخدمة تعمل")
        else:
            print("🔴 الخدمة متوقفة")

def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='خدمة مجدول المهام لنظام إدارة السائقين',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python run_scheduler.py start          # بدء الخدمة
  python run_scheduler.py stop           # إيقاف الخدمة
  python run_scheduler.py status         # عرض حالة الخدمة
  python run_scheduler.py --daemon       # تشغيل كخدمة خلفية
        """
    )
    
    parser.add_argument('action', nargs='?', choices=['start', 'stop', 'status'], 
                       default='start', help='الإجراء المطلوب')
    parser.add_argument('--daemon', action='store_true', 
                       help='تشغيل كخدمة خلفية')
    parser.add_argument('--pid-file', default='scheduler.pid',
                       help='ملف معرف العملية')
    
    args = parser.parse_args()
    
    service = SchedulerService()
    
    if args.action == 'start':
        if args.daemon:
            # تشغيل كخدمة خلفية (يحتاج تطوير إضافي)
            print("تشغيل الخدمة كخلفية...")
            # يمكن استخدام مكتبات مثل python-daemon
            service.start()
        else:
            service.start()
    
    elif args.action == 'stop':
        service.stop()
    
    elif args.action == 'status':
        service.status()

if __name__ == "__main__":
    main()