{% extends "base.html" %}

{% block title %}إدارة الأدوار{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-user-tag"></i> إدارة الأدوار والصلاحيات</h2>
        <div>
          <button onclick="createRole()" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة دور جديد
          </button>
          <a href="{{ url_for('user_management.users_list') }}" class="btn btn-outline-secondary">
            <i class="fas fa-users"></i> المستخدمين
          </a>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات سريعة -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-primary">{{ roles|length }}</h4>
          <small class="text-muted">إجمالي الأدوار</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-success">{{ roles|selectattr('is_active')|list|length }}</h4>
          <small class="text-muted">أدوار نشطة</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-info">{{ roles|sum(attribute='users')|length if roles else 0 }}</h4>
          <small class="text-muted">مستخدمين مرتبطين</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-warning">4</h4>
          <small class="text-muted">أدوار افتراضية</small>
        </div>
      </div>
    </div>
  </div>

  <!-- قائمة الأدوار -->
  <div class="row">
    {% for role in roles %}
    <div class="col-md-6 col-lg-4 mb-4">
      <div class="card h-100 {{ 'border-primary' if role.name == 'admin' else '' }}">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h6 class="mb-0">
            <i class="fas fa-user-tag"></i> {{ role.display_name or role.name }}
            {% if role.name == 'admin' %}
              <span class="badge bg-danger ms-1">رئيسي</span>
            {% endif %}
          </h6>
          <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                    data-bs-toggle="dropdown">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="viewRole({{ role.id }})">
                <i class="fas fa-eye"></i> عرض التفاصيل
              </a></li>
              {% if role.name != 'admin' %}
                <li><a class="dropdown-item" href="#" onclick="editRole({{ role.id }})">
                  <i class="fas fa-edit"></i> تعديل
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" onclick="deleteRole({{ role.id }}, '{{ role.display_name or role.name }}')">
                  <i class="fas fa-trash"></i> حذف
                </a></li>
              {% endif %}
            </ul>
          </div>
        </div>
        <div class="card-body">
          <p class="card-text text-muted">{{ role.description or 'بدون وصف' }}</p>
          
          <!-- الصلاحيات -->
          <div class="mb-3">
            <small class="text-muted">الصلاحيات:</small>
            <div class="mt-1">
              {% if role.permissions %}
                {% set permissions = role.permissions|from_json if role.permissions else [] %}
                {% for permission in permissions[:3] %}
                  <span class="badge bg-secondary me-1 mb-1">{{ permission }}</span>
                {% endfor %}
                {% if permissions|length > 3 %}
                  <span class="badge bg-light text-dark">+{{ permissions|length - 3 }} أخرى</span>
                {% endif %}
              {% else %}
                <span class="text-muted">لا توجد صلاحيات</span>
              {% endif %}
            </div>
          </div>
          
          <!-- المستخدمين -->
          <div class="mb-3">
            <small class="text-muted">المستخدمين:</small>
            <div class="mt-1">
              <span class="badge bg-info">{{ role.users|length }} مستخدم</span>
            </div>
          </div>
          
          <!-- الحالة -->
          <div class="mb-3">
            <small class="text-muted">الحالة:</small>
            <div class="mt-1">
              {% if role.is_active %}
                <span class="badge bg-success">نشط</span>
              {% else %}
                <span class="badge bg-secondary">معطل</span>
              {% endif %}
            </div>
          </div>
          
          <!-- تاريخ الإنشاء -->
          <div>
            <small class="text-muted">
              أُنشئ في: {{ moment(role.created_at).format('YYYY-MM-DD') if moment and role.created_at else (role.created_at.strftime('%Y-%m-%d') if role.created_at else 'غير محدد') }}
            </small>
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  {% if not roles %}
  <div class="row">
    <div class="col-12">
      <div class="text-center py-5">
        <i class="fas fa-user-tag fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لا توجد أدوار</h5>
        <p class="text-muted">قم بإضافة أول دور للبدء</p>
        <button onclick="createRole()" class="btn btn-primary">
          <i class="fas fa-plus"></i> إضافة دور جديد
        </button>
      </div>
    </div>
  </div>
  {% endif %}
</div>

<!-- Modal عرض تفاصيل الدور -->
<div class="modal fade" id="roleDetailsModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تفاصيل الدور</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="roleDetailsContent">
        <!-- سيتم تحميل المحتوى هنا -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal إنشاء/تعديل الدور -->
<div class="modal fade" id="roleFormModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="roleFormTitle">إضافة دور جديد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="roleForm">
          <input type="hidden" id="roleId" name="role_id">
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="roleName" class="form-label">اسم الدور (بالإنجليزية) <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="roleName" name="name" required>
              <div class="form-text">يُستخدم داخلياً، يجب أن يكون فريداً</div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="roleDisplayName" class="form-label">الاسم المعروض <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="roleDisplayName" name="display_name" required>
            </div>
          </div>
          
          <div class="mb-3">
            <label for="roleDescription" class="form-label">الوصف</label>
            <textarea class="form-control" id="roleDescription" name="description" rows="3"></textarea>
          </div>
          
          <div class="mb-3">
            <label class="form-label">الصلاحيات</label>
            <div class="row" id="permissionsContainer">
              <!-- سيتم تحميل الصلاحيات هنا -->
            </div>
          </div>
          
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="roleIsActive" name="is_active" checked>
            <label class="form-check-label" for="roleIsActive">الدور نشط</label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="saveRole()">
          <i class="fas fa-save"></i> حفظ
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// قائمة الصلاحيات المتاحة
const availablePermissions = [
  { id: 'view_drivers', name: 'عرض السائقين', category: 'السائقين' },
  { id: 'create_drivers', name: 'إضافة السائقين', category: 'السائقين' },
  { id: 'edit_drivers', name: 'تعديل السائقين', category: 'السائقين' },
  { id: 'delete_drivers', name: 'حذف السائقين', category: 'السائقين' },
  
  { id: 'view_vehicles', name: 'عرض المركبات', category: 'المركبات' },
  { id: 'create_vehicles', name: 'إضافة المركبات', category: 'المركبات' },
  { id: 'edit_vehicles', name: 'تعديل المركبات', category: 'المركبات' },
  { id: 'delete_vehicles', name: 'حذف المركبات', category: 'المركبات' },
  
  { id: 'view_payments', name: 'عرض المدفوعات', category: 'المدفوعات' },
  { id: 'create_payments', name: 'إضافة المدفوعات', category: 'المدفوعات' },
  { id: 'edit_payments', name: 'تعديل المدفوعات', category: 'المدفوعات' },
  { id: 'delete_payments', name: 'حذف المدفوعات', category: 'المدفوعات' },
  
  { id: 'view_expenses', name: 'عرض المصروفات', category: 'المصروفات' },
  { id: 'create_expenses', name: 'إضافة المصروفات', category: 'المصروفات' },
  { id: 'edit_expenses', name: 'تعديل المصروفات', category: 'المصروفات' },
  { id: 'delete_expenses', name: 'حذف المصروفات', category: 'المصروفات' },
  
  { id: 'view_reports', name: 'عرض التقارير', category: 'التقارير' },
  { id: 'export_reports', name: 'تصدير التقارير', category: 'التقارير' },
  
  { id: 'view_system_settings', name: 'عرض إعدادات النظام', category: 'النظام' },
  { id: 'edit_system_settings', name: 'تعديل إعدادات النظام', category: 'النظام' },
  { id: 'view_system_logs', name: 'عرض سجلات النظام', category: 'النظام' },
  { id: 'manage_backups', name: 'إدارة النسخ الاحتياطية', category: 'النظام' },
  
  { id: 'view_users', name: 'عرض المستخدمين', category: 'إدارة المستخدمين' },
  { id: 'create_users', name: 'إضافة المستخدمين', category: 'إدارة المستخدمين' },
  { id: 'edit_users', name: 'تعديل المستخدمين', category: 'إدارة المستخدمين' },
  { id: 'delete_users', name: 'حذف المستخدمين', category: 'إدارة المستخدمين' },
  { id: 'manage_roles', name: 'إدارة الأدوار', category: 'إدارة المستخدمين' }
];

// بيانات الأدوار
const rolesData = {
  {% for role in roles %}
  {{ role.id }}: {
    id: {{ role.id }},
    name: '{{ role.name }}',
    display_name: '{{ role.display_name or role.name }}',
    description: '{{ role.description or "" }}',
    permissions: {{ role.permissions|safe if role.permissions else '[]' }},
    is_active: {{ role.is_active|lower }},
    users_count: {{ role.users|length }}
  }{% if not loop.last %},{% endif %}
  {% endfor %}
};

// عرض تفاصيل الدور
function viewRole(roleId) {
  const role = rolesData[roleId];
  if (!role) return;
  
  let content = `
    <div class="row">
      <div class="col-md-6">
        <h6>معلومات أساسية</h6>
        <table class="table table-sm">
          <tr><td><strong>الاسم:</strong></td><td>${role.display_name}</td></tr>
          <tr><td><strong>الاسم الداخلي:</strong></td><td><code>${role.name}</code></td></tr>
          <tr><td><strong>الوصف:</strong></td><td>${role.description || 'بدون وصف'}</td></tr>
          <tr><td><strong>الحالة:</strong></td><td>${role.is_active ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-secondary">معطل</span>'}</td></tr>
          <tr><td><strong>المستخدمين:</strong></td><td><span class="badge bg-info">${role.users_count} مستخدم</span></td></tr>
        </table>
      </div>
      <div class="col-md-6">
        <h6>الصلاحيات (${role.permissions.length})</h6>
        <div style="max-height: 300px; overflow-y: auto;">
  `;
  
  if (role.permissions.length > 0) {
    // تجميع الصلاحيات حسب الفئة
    const permissionsByCategory = {};
    role.permissions.forEach(permId => {
      const perm = availablePermissions.find(p => p.id === permId);
      if (perm) {
        if (!permissionsByCategory[perm.category]) {
          permissionsByCategory[perm.category] = [];
        }
        permissionsByCategory[perm.category].push(perm);
      }
    });
    
    Object.keys(permissionsByCategory).forEach(category => {
      content += `<h6 class="mt-3 mb-2">${category}</h6>`;
      permissionsByCategory[category].forEach(perm => {
        content += `<span class="badge bg-secondary me-1 mb-1">${perm.name}</span>`;
      });
    });
  } else {
    content += '<p class="text-muted">لا توجد صلاحيات محددة</p>';
  }
  
  content += `
        </div>
      </div>
    </div>
  `;
  
  document.getElementById('roleDetailsContent').innerHTML = content;
  const modal = new bootstrap.Modal(document.getElementById('roleDetailsModal'));
  modal.show();
}

// إنشاء دور جديد
function createRole() {
  document.getElementById('roleFormTitle').textContent = 'إضافة دور جديد';
  document.getElementById('roleForm').reset();
  document.getElementById('roleId').value = '';
  
  loadPermissions();
  
  const modal = new bootstrap.Modal(document.getElementById('roleFormModal'));
  modal.show();
}

// تعديل الدور
function editRole(roleId) {
  const role = rolesData[roleId];
  if (!role) return;
  
  document.getElementById('roleFormTitle').textContent = 'تعديل الدور';
  document.getElementById('roleId').value = role.id;
  document.getElementById('roleName').value = role.name;
  document.getElementById('roleDisplayName').value = role.display_name;
  document.getElementById('roleDescription').value = role.description;
  document.getElementById('roleIsActive').checked = role.is_active;
  
  loadPermissions(role.permissions);
  
  const modal = new bootstrap.Modal(document.getElementById('roleFormModal'));
  modal.show();
}

// تحميل الصلاحيات
function loadPermissions(selectedPermissions = []) {
  const container = document.getElementById('permissionsContainer');
  
  // تجميع الصلاحيات حسب الفئة
  const permissionsByCategory = {};
  availablePermissions.forEach(perm => {
    if (!permissionsByCategory[perm.category]) {
      permissionsByCategory[perm.category] = [];
    }
    permissionsByCategory[perm.category].push(perm);
  });
  
  let html = '';
  Object.keys(permissionsByCategory).forEach(category => {
    html += `
      <div class="col-12 mb-3">
        <h6>${category}</h6>
        <div class="row">
    `;
    
    permissionsByCategory[category].forEach(perm => {
      const isChecked = selectedPermissions.includes(perm.id);
      html += `
        <div class="col-md-6 col-lg-4">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="perm_${perm.id}" 
                   name="permissions" value="${perm.id}" ${isChecked ? 'checked' : ''}>
            <label class="form-check-label" for="perm_${perm.id}">
              ${perm.name}
            </label>
          </div>
        </div>
      `;
    });
    
    html += `
        </div>
      </div>
    `;
  });
  
  container.innerHTML = html;
}

// حفظ الدور
function saveRole() {
  const form = document.getElementById('roleForm');
  const formData = new FormData(form);
  
  // جمع الصلاحيات المحددة
  const permissions = [];
  document.querySelectorAll('input[name="permissions"]:checked').forEach(checkbox => {
    permissions.push(checkbox.value);
  });
  
  const data = {
    name: formData.get('name'),
    display_name: formData.get('display_name'),
    description: formData.get('description'),
    permissions: permissions,
    is_active: formData.has('is_active')
  };
  
  const roleId = formData.get('role_id');
  const url = roleId ? `/admin/roles/${roleId}/edit` : '/admin/roles/create';
  const method = 'POST';
  
  fetch(url, {
    method: method,
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(roleId ? 'تم تحديث الدور بنجاح' : 'تم إنشاء الدور بنجاح');
      location.reload();
    } else {
      alert('خطأ: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في حفظ الدور');
  });
}

// حذف الدور
function deleteRole(roleId, roleName) {
  const role = rolesData[roleId];
  
  if (role.users_count > 0) {
    alert(`لا يمكن حذف الدور "${roleName}" لأنه مرتبط بـ ${role.users_count} مستخدم`);
    return;
  }
  
  if (confirm(`هل أنت متأكد من حذف الدور "${roleName}"؟\n\nلا يمكن التراجع عن هذا الإجراء!`)) {
    fetch(`/admin/roles/${roleId}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('تم حذف الدور بنجاح');
        location.reload();
      } else {
        alert('خطأ: ' + data.error);
      }
    })
    .catch(error => {
      console.error('خطأ:', error);
      alert('حدث خطأ في حذف الدور');
    });
  }
}
</script>

<style>
.card {
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

.border-primary {
  border-width: 2px !important;
}
</style>
{% endblock %}