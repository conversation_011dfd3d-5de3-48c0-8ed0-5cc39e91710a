<!-- templates/reports.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>التقارير</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <a href="{{ url_for('dashboard') }}" class="btn btn-light me-2">السائقون</a>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2 active">التقارير</a>
      <a href="{{ url_for('analytics') }}" class="btn btn-outline-light me-2">الإحصائيات</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light">الإعدادات</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">التقارير والإحصائيات</h4>
    <div class="d-flex gap-2">
      <div class="dropdown">
        <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
          <i class="fas fa-download"></i> تصدير
        </button>
        <ul class="dropdown-menu">
          <li><a class="dropdown-item" href="{{ url_for('export_drivers') }}">تصدير السائقين</a></li>
          <li><a class="dropdown-item" href="{{ url_for('export_payments') }}">تصدير المدفوعات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('export_expenses') }}">تصدير المصروفات</a></li>
        </ul>
      </div>
      <div class="dropdown">
        <button class="btn btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
          <i class="fas fa-print"></i> طباعة
        </button>
        <ul class="dropdown-menu">
          <li><a class="dropdown-item" href="{{ url_for('print_report', type='drivers') }}" target="_blank">طباعة تقرير السائقين</a></li>
          <li><a class="dropdown-item" href="{{ url_for('print_report', type='payments') }}" target="_blank">طباعة تقرير المدفوعات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('print_report', type='expenses') }}" target="_blank">طباعة تقرير المصروفات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('print_report', type='salaries') }}" target="_blank">طباعة تقرير الرواتب</a></li>
        </ul>
      </div>
    </div>
  </div>

  <!-- إحصائيات عامة -->
  <div class="row g-3 mb-4">
    <div class="col-md-3">
      <div class="card text-center shadow-sm bg-primary text-white">
        <div class="card-body">
          <div class="h2 mb-1">{{ total_drivers }}</div>
          <div>إجمالي السائقين</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center shadow-sm bg-success text-white">
        <div class="card-body">
          <div class="h2 mb-1">{{ total_vehicles }}</div>
          <div>إجمالي المركبات</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center shadow-sm bg-info text-white">
        <div class="card-body">
          <div class="h2 mb-1">{{ total_payments_count }}</div>
          <div>إجمالي المدفوعات</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center shadow-sm bg-warning text-white">
        <div class="card-body">
          <div class="h2 mb-1">{{ total_expenses_count }}</div>
          <div>إجمالي المصروفات</div>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات مالية -->
  <div class="row g-3 mb-4">
    <div class="col-md-4">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <div class="h4 mb-1 text-success">{{ "%.2f"|format(total_balance) }}</div>
          <div class="text-muted">إجمالي أرصدة السائقين</div>
          <small class="text-muted">متوسط: {{ "%.2f"|format(avg_balance) }}</small>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <div class="h4 mb-1 text-primary">{{ "%.2f"|format(total_payments_in) }}</div>
          <div class="text-muted">إجمالي المدفوعات الداخلة</div>
          <div class="h4 mb-1 text-danger">{{ "%.2f"|format(total_payments_out) }}</div>
          <div class="text-muted">إجمالي المدفوعات الخارجة</div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card text-center shadow-sm">
        <div class="card-body">
          <div class="h4 mb-1 text-info">{{ "%.2f"|format(total_salaries) }}</div>
          <div class="text-muted">إجمالي الرواتب</div>
          <div class="h4 mb-1 text-warning">{{ "%.2f"|format(total_expenses) }}</div>
          <div class="text-muted">إجمالي المصروفات</div>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات المركبات -->
  <div class="row g-3 mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">حالة المركبات</h5>
          <div class="row text-center">
            <div class="col-6">
              <div class="h3 text-success">{{ assigned_vehicles }}</div>
              <div class="text-muted">مخصصة لسائقين</div>
            </div>
            <div class="col-6">
              <div class="h3 text-warning">{{ unassigned_vehicles }}</div>
              <div class="text-muted">غير مخصصة</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">المصروفات حسب الفئة</h5>
          {% if expense_categories %}
            {% for category in expense_categories %}
              <div class="d-flex justify-content-between mb-2">
                <span>{{ category.category }}</span>
                <span class="fw-bold">{{ "%.2f"|format(category.total) }}</span>
              </div>
            {% endfor %}
          {% else %}
            <p class="text-muted">لا توجد مصروفات مصنفة</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- أحدث العمليات -->
  <div class="row g-3">
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">أحدث السائقين</h5>
          <div class="table-responsive">
            <table class="table table-sm">
              <tbody>
                {% for d in latest_drivers %}
                  <tr>
                    <td><a href="{{ url_for('driver_detail', driver_id=d.id) }}">{{ d.name }}</a></td>
                    <td class="text-end">{{ "%.2f"|format(d.balance) }}</td>
                  </tr>
                {% else %}
                  <tr>
                    <td colspan="2" class="text-center text-muted">لا توجد بيانات</td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">أحدث المدفوعات</h5>
          <div class="table-responsive">
            <table class="table table-sm">
              <tbody>
                {% for p in latest_payments %}
                  <tr>
                    <td>{{ p.driver.name }}</td>
                    <td class="text-end">
                      {% if p.direction == 'in' %}
                        <span class="text-success">+{{ "%.2f"|format(p.amount) }}</span>
                      {% else %}
                        <span class="text-danger">-{{ "%.2f"|format(p.amount) }}</span>
                      {% endif %}
                    </td>
                  </tr>
                {% else %}
                  <tr>
                    <td colspan="2" class="text-center text-muted">لا توجد بيانات</td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">أحدث المصروفات</h5>
          <div class="table-responsive">
            <table class="table table-sm">
              <tbody>
                {% for e in latest_expenses %}
                  <tr>
                    <td>{{ e.category or 'غير محدد' }}</td>
                    <td class="text-end">{{ "%.2f"|format(e.amount) }}</td>
                  </tr>
                {% else %}
                  <tr>
                    <td colspan="2" class="text-center text-muted">لا توجد بيانات</td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>