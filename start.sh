#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    نظام إدارة السائقين والمركبات${NC}"
echo -e "${BLUE}    Driver Management System${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 غير مثبت${NC}"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

echo -e "${GREEN}✅ تم العثور على Python3${NC}"

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    echo -e "${RED}❌ pip3 غير متاح${NC}"
    echo "يرجى تثبيت pip3"
    exit 1
fi

echo -e "${GREEN}✅ تم العثور على pip3${NC}"

# التحقق من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}🔧 إنشاء البيئة الافتراضية...${NC}"
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ فشل في إنشاء البيئة الافتراضية${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ تم إنشاء البيئة الافتراضية${NC}"
fi

# تفعيل البيئة الافتراضية
echo -e "${YELLOW}🔧 تفعيل البيئة الافتراضية...${NC}"
source venv/bin/activate
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ فشل في تفعيل البيئة الافتراضية${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم تفعيل البيئة الافتراضية${NC}"

# تثبيت المتطلبات
if [ -f "requirements.txt" ]; then
    echo -e "${YELLOW}📦 تثبيت المتطلبات...${NC}"
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ فشل في تثبيت المتطلبات${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ تم تثبيت المتطلبات${NC}"
else
    echo -e "${YELLOW}⚠️  ملف requirements.txt غير موجود${NC}"
fi

# التحقق من وجود قاعدة البيانات
if [ ! -f "drivers.db" ]; then
    echo -e "${YELLOW}🗄️  إعداد قاعدة البيانات...${NC}"
    python init_db.py init
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ فشل في إعداد قاعدة البيانات${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ تم إعداد قاعدة البيانات${NC}"
fi

echo
echo -e "${GREEN}🚀 بدء تشغيل النظام...${NC}"
echo -e "${BLUE}📍 الرابط: http://localhost:5000${NC}"
echo -e "${BLUE}👤 اسم المستخدم: admin${NC}"
echo -e "${BLUE}🔑 كلمة المرور: admin123${NC}"
echo
echo -e "${YELLOW}للإيقاف اضغط Ctrl+C${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# تشغيل التطبيق
python app.py

echo
echo -e "${GREEN}تم إيقاف النظام${NC}"