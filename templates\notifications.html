{% extends "base.html" %}

{% block title %}الإشعارات{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-bell"></i> الإشعارات</h2>
        <div>
          <button onclick="markAllAsRead()" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-check-double"></i> تمييز الكل كمقروء
          </button>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-right"></i> العودة
          </a>
        </div>
      </div>

      <!-- فلاتر الإشعارات -->
      <div class="card mb-4">
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-3">
              <select id="typeFilter" class="form-select" onchange="filterNotifications()">
                <option value="">جميع الأنواع</option>
                <option value="info">معلومات</option>
                <option value="success">نجاح</option>
                <option value="warning">تحذير</option>
                <option value="error">خطأ</option>
              </select>
            </div>
            <div class="col-md-3">
              <select id="statusFilter" class="form-select" onchange="filterNotifications()">
                <option value="">جميع الحالات</option>
                <option value="unread">غير مقروء</option>
                <option value="read">مقروء</option>
              </select>
            </div>
            <div class="col-md-6">
              <input type="text" id="searchInput" class="form-control" placeholder="البحث في الإشعارات..." onkeyup="filterNotifications()">
            </div>
          </div>
        </div>
      </div>

      <!-- قائمة الإشعارات -->
      <div id="notificationsList">
        {% if notifications %}
          {% for notification in notifications %}
          <div class="card mb-3 notification-item {{ 'border-primary' if not notification.read else '' }}" 
               data-type="{{ notification.type }}" 
               data-status="{{ 'unread' if not notification.read else 'read' }}"
               data-id="{{ notification.id }}">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                  <div class="d-flex align-items-center mb-2">
                    <!-- أيقونة حسب النوع -->
                    {% if notification.type == 'success' %}
                      <i class="fas fa-check-circle text-success me-2"></i>
                    {% elif notification.type == 'warning' %}
                      <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    {% elif notification.type == 'error' %}
                      <i class="fas fa-times-circle text-danger me-2"></i>
                    {% else %}
                      <i class="fas fa-info-circle text-info me-2"></i>
                    {% endif %}
                    
                    <h6 class="mb-0 {{ 'fw-bold' if not notification.read else '' }}">
                      {{ notification.title }}
                    </h6>
                    
                    {% if not notification.read %}
                      <span class="badge bg-primary ms-2">جديد</span>
                    {% endif %}
                  </div>
                  
                  <p class="mb-2 text-muted">{{ notification.message }}</p>
                  
                  <small class="text-muted">
                    <i class="fas fa-clock"></i>
                    {{ moment(notification.created_at).format('YYYY-MM-DD HH:mm') if moment else notification.created_at[:16] }}
                  </small>
                </div>
                
                <div class="dropdown">
                  <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                  <ul class="dropdown-menu">
                    {% if not notification.read %}
                    <li>
                      <a class="dropdown-item" href="#" onclick="markAsRead({{ notification.id }})">
                        <i class="fas fa-check"></i> تمييز كمقروء
                      </a>
                    </li>
                    {% endif %}
                    <li>
                      <a class="dropdown-item text-danger" href="#" onclick="deleteNotification({{ notification.id }})">
                        <i class="fas fa-trash"></i> حذف
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              
              <!-- بيانات إضافية إن وجدت -->
              {% if notification.data %}
              <div class="mt-3">
                <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#details{{ notification.id }}">
                  <i class="fas fa-info"></i> تفاصيل إضافية
                </button>
                <div class="collapse mt-2" id="details{{ notification.id }}">
                  <div class="card card-body bg-light">
                    <pre class="mb-0">{{ notification.data | tojson(indent=2) }}</pre>
                  </div>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
          {% endfor %}
        {% else %}
          <div class="text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد إشعارات</h5>
            <p class="text-muted">ستظهر الإشعارات هنا عند توفرها</p>
          </div>
        {% endif %}
      </div>

      <!-- تحميل المزيد -->
      {% if notifications and notifications|length >= 20 %}
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary" onclick="loadMoreNotifications()">
          <i class="fas fa-plus"></i> تحميل المزيد
        </button>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
// تمييز إشعار كمقروء
function markAsRead(notificationId) {
  fetch(`/api/notifications/${notificationId}/read`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // تحديث واجهة المستخدم
      const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
      if (notificationElement) {
        notificationElement.classList.remove('border-primary');
        notificationElement.setAttribute('data-status', 'read');
        
        // إزالة علامة "جديد"
        const newBadge = notificationElement.querySelector('.badge.bg-primary');
        if (newBadge) {
          newBadge.remove();
        }
        
        // إزالة التمييز الغامق
        const title = notificationElement.querySelector('h6');
        if (title) {
          title.classList.remove('fw-bold');
        }
      }
      
      // تحديث عداد الإشعارات في الشريط العلوي
      updateNotificationCount();
    }
  })
  .catch(error => {
    console.error('خطأ في تمييز الإشعار:', error);
    alert('حدث خطأ في تمييز الإشعار');
  });
}

// تمييز جميع الإشعارات كمقروءة
function markAllAsRead() {
  const unreadNotifications = document.querySelectorAll('[data-status="unread"]');
  
  if (unreadNotifications.length === 0) {
    alert('جميع الإشعارات مقروءة بالفعل');
    return;
  }
  
  if (confirm(`هل تريد تمييز ${unreadNotifications.length} إشعار كمقروء؟`)) {
    unreadNotifications.forEach(notification => {
      const notificationId = notification.getAttribute('data-id');
      markAsRead(notificationId);
    });
  }
}

// حذف إشعار
function deleteNotification(notificationId) {
  if (confirm('هل تريد حذف هذا الإشعار؟')) {
    // هنا يمكن إضافة API لحذف الإشعار
    const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
    if (notificationElement) {
      notificationElement.style.transition = 'opacity 0.3s';
      notificationElement.style.opacity = '0';
      setTimeout(() => {
        notificationElement.remove();
      }, 300);
    }
  }
}

// فلترة الإشعارات
function filterNotifications() {
  const typeFilter = document.getElementById('typeFilter').value;
  const statusFilter = document.getElementById('statusFilter').value;
  const searchInput = document.getElementById('searchInput').value.toLowerCase();
  
  const notifications = document.querySelectorAll('.notification-item');
  
  notifications.forEach(notification => {
    const type = notification.getAttribute('data-type');
    const status = notification.getAttribute('data-status');
    const title = notification.querySelector('h6').textContent.toLowerCase();
    const message = notification.querySelector('p').textContent.toLowerCase();
    
    let show = true;
    
    // فلتر النوع
    if (typeFilter && type !== typeFilter) {
      show = false;
    }
    
    // فلتر الحالة
    if (statusFilter && status !== statusFilter) {
      show = false;
    }
    
    // فلتر البحث
    if (searchInput && !title.includes(searchInput) && !message.includes(searchInput)) {
      show = false;
    }
    
    notification.style.display = show ? 'block' : 'none';
  });
}

// تحديث عداد الإشعارات
function updateNotificationCount() {
  const unreadCount = document.querySelectorAll('[data-status="unread"]').length;
  const badge = document.querySelector('.notification-badge');
  
  if (badge) {
    if (unreadCount > 0) {
      badge.textContent = unreadCount;
      badge.style.display = 'inline';
    } else {
      badge.style.display = 'none';
    }
  }
}

// تحميل المزيد من الإشعارات
function loadMoreNotifications() {
  // يمكن تطبيق هذه الوظيفة لاحقاً مع pagination
  alert('ميزة تحميل المزيد ستكون متاحة قريباً');
}

// تحديث الإشعارات كل دقيقة
setInterval(() => {
  // يمكن إضافة تحديث تلقائي للإشعارات هنا
}, 60000);
</script>
{% endblock %}