{% extends "base.html" %}

{% block title %}إدارة النسخ الاحتياطية{% endblock %}

{% block extra_css %}
<style>
.backup-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.backup-item:hover {
    border-left-color: #007bff;
    background-color: #f8f9fa;
}

.backup-type-auto { border-left-color: #28a745; }
.backup-type-manual { border-left-color: #007bff; }

.file-size {
    font-family: 'Courier New', monospace;
}

.backup-actions .btn {
    margin: 2px;
}

.progress-container {
    display: none;
    margin-top: 15px;
}

.config-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-database"></i> إدارة النسخ الاحتياطية</h2>
        <div>
          <button onclick="createBackup()" class="btn btn-primary">
            <i class="fas fa-plus"></i> إنشاء نسخة احتياطية
          </button>
          <button onclick="refreshBackups()" class="btn btn-outline-secondary">
            <i class="fas fa-sync-alt"></i> تحديث
          </button>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- إعدادات النسخ الاحتياطية -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fas fa-cog"></i> إعدادات النسخ الاحتياطية
            <button class="btn btn-sm btn-outline-primary float-end" onclick="toggleConfig()">
              <i class="fas fa-edit"></i> تعديل
            </button>
          </h6>
        </div>
        <div class="card-body config-section">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label>أقصى عدد نسخ</label>
                <input type="number" class="form-control" id="maxBackups" value="{{ config.max_backups }}" readonly>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-check form-switch mt-4">
                <input class="form-check-input" type="checkbox" id="compressBackups" 
                       {{ 'checked' if config.compress else '' }} disabled>
                <label class="form-check-label">ضغط النسخ</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-check form-switch mt-4">
                <input class="form-check-input" type="checkbox" id="encryptBackups" 
                       {{ 'checked' if config.encrypt else '' }} disabled>
                <label class="form-check-label">تشفير النسخ</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-check form-switch mt-4">
                <input class="form-check-input" type="checkbox" id="includeLogs" 
                       {{ 'checked' if config.include_logs else '' }} disabled>
                <label class="form-check-label">تضمين السجلات</label>
              </div>
            </div>
          </div>
          <div class="row mt-3" id="configActions" style="display: none;">
            <div class="col-12">
              <button onclick="saveConfig()" class="btn btn-success btn-sm">
                <i class="fas fa-save"></i> حفظ
              </button>
              <button onclick="cancelConfig()" class="btn btn-secondary btn-sm">
                <i class="fas fa-times"></i> إلغاء
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- شريط التقدم -->
  <div class="progress-container">
    <div class="card">
      <div class="card-body">
        <h6 id="progressTitle">جاري العمل...</h6>
        <div class="progress">
          <div class="progress-bar progress-bar-striped progress-bar-animated" 
               id="progressBar" style="width: 0%"></div>
        </div>
        <small id="progressText" class="text-muted">يرجى الانتظار...</small>
      </div>
    </div>
  </div>

  <!-- إحصائيات سريعة -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-primary">{{ backups|length }}</h4>
          <small class="text-muted">إجمالي النسخ</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-success">{{ backups|selectattr('type', 'equalto', 'auto_daily')|list|length + backups|selectattr('type', 'equalto', 'auto_weekly')|list|length }}</h4>
          <small class="text-muted">نسخ تلقائية</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-info">{{ backups|selectattr('type', 'equalto', 'manual')|list|length }}</h4>
          <small class="text-muted">نسخ يدوية</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-warning">{{ "%.1f"|format((backups|sum(attribute='file_size')|default(0)) / 1024 / 1024) }} MB</h4>
          <small class="text-muted">الحجم الإجمالي</small>
        </div>
      </div>
    </div>
  </div>

  <!-- قائمة النسخ الاحتياطية -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-list"></i> النسخ الاحتياطية المتاحة</h6>
        </div>
        <div class="card-body">
          {% if backups %}
            <div id="backupsList">
              {% for backup in backups %}
              <div class="backup-item card mb-3 backup-type-{{ backup.type.split('_')[0] }}">
                <div class="card-body">
                  <div class="row align-items-center">
                    <div class="col-md-4">
                      <h6 class="mb-1">
                        {% if backup.type.startswith('auto') %}
                          <i class="fas fa-robot text-success"></i>
                        {% else %}
                          <i class="fas fa-user text-primary"></i>
                        {% endif %}
                        {{ backup.name }}
                      </h6>
                      <small class="text-muted">{{ backup.description or 'بدون وصف' }}</small>
                    </div>
                    
                    <div class="col-md-2">
                      <small class="text-muted">التاريخ</small><br>
                      <span>{{ moment(backup.created_at).format('YYYY-MM-DD') if moment else backup.created_at[:10] }}</span><br>
                      <small class="text-muted">{{ moment(backup.created_at).format('HH:mm') if moment else backup.created_at[11:16] }}</small>
                    </div>
                    
                    <div class="col-md-2">
                      <small class="text-muted">الحجم</small><br>
                      <span class="file-size">{{ "%.2f"|format(backup.file_size / 1024 / 1024) }} MB</span>
                    </div>
                    
                    <div class="col-md-2">
                      <small class="text-muted">الحالة</small><br>
                      {% if backup.encrypted %}
                        <span class="badge bg-warning"><i class="fas fa-lock"></i> مشفر</span>
                      {% endif %}
                      {% if backup.compressed %}
                        <span class="badge bg-info"><i class="fas fa-compress"></i> مضغوط</span>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-2">
                      <div class="backup-actions">
                        <button onclick="verifyBackup('{{ backup.file_path }}')" 
                                class="btn btn-sm btn-outline-success" title="التحقق من السلامة">
                          <i class="fas fa-check-circle"></i>
                        </button>
                        <button onclick="restoreBackup('{{ backup.file_path }}', '{{ backup.name }}')" 
                                class="btn btn-sm btn-outline-primary" title="استعادة">
                          <i class="fas fa-undo"></i>
                        </button>
                        <button onclick="downloadBackup('{{ backup.file_path }}')" 
                                class="btn btn-sm btn-outline-info" title="تحميل">
                          <i class="fas fa-download"></i>
                        </button>
                        <button onclick="deleteBackup('{{ backup.file_path }}', '{{ backup.name }}')" 
                                class="btn btn-sm btn-outline-danger" title="حذف">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-database fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
              <p class="text-muted">قم بإنشاء أول نسخة احتياطية للبدء</p>
              <button onclick="createBackup()" class="btn btn-primary">
                <i class="fas fa-plus"></i> إنشاء نسخة احتياطية
              </button>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal إنشاء نسخة احتياطية -->
<div class="modal fade" id="createBackupModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">إنشاء نسخة احتياطية جديدة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="createBackupForm">
          <div class="mb-3">
            <label for="backupDescription" class="form-label">وصف النسخة الاحتياطية</label>
            <textarea class="form-control" id="backupDescription" rows="3" 
                      placeholder="اكتب وصفاً للنسخة الاحتياطية (اختياري)"></textarea>
          </div>
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>ملاحظة:</strong> قد تستغرق عملية إنشاء النسخة الاحتياطية بضع دقائق حسب حجم البيانات.
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" onclick="confirmCreateBackup()">
          <i class="fas fa-database"></i> إنشاء النسخة الاحتياطية
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// إنشاء نسخة احتياطية
function createBackup() {
  const modal = new bootstrap.Modal(document.getElementById('createBackupModal'));
  modal.show();
}

function confirmCreateBackup() {
  const description = document.getElementById('backupDescription').value;
  const modal = bootstrap.Modal.getInstance(document.getElementById('createBackupModal'));
  
  modal.hide();
  showProgress('إنشاء نسخة احتياطية', 'جاري إنشاء النسخة الاحتياطية...');
  
  fetch('/api/backup/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      description: description
    })
  })
  .then(response => response.json())
  .then(data => {
    hideProgress();
    
    if (data.success) {
      alert('تم إنشاء النسخة الاحتياطية بنجاح');
      location.reload();
    } else {
      alert('خطأ في إنشاء النسخة الاحتياطية: ' + data.error);
    }
  })
  .catch(error => {
    hideProgress();
    console.error('خطأ:', error);
    alert('حدث خطأ في إنشاء النسخة الاحتياطية');
  });
}

// التحقق من سلامة النسخة الاحتياطية
function verifyBackup(backupFile) {
  showProgress('التحقق من السلامة', 'جاري التحقق من سلامة النسخة الاحتياطية...');
  
  fetch('/api/backup/verify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      backup_file: backupFile
    })
  })
  .then(response => response.json())
  .then(data => {
    hideProgress();
    
    if (data.success) {
      const verification = data.verification;
      if (verification.valid) {
        alert('✅ النسخة الاحتياطية سليمة وصالحة للاستخدام');
      } else {
        alert('❌ النسخة الاحتياطية تالفة: ' + verification.error);
      }
    } else {
      alert('خطأ في التحقق: ' + data.error);
    }
  })
  .catch(error => {
    hideProgress();
    console.error('خطأ:', error);
    alert('حدث خطأ في التحقق من النسخة الاحتياطية');
  });
}

// استعادة النسخة الاحتياطية
function restoreBackup(backupFile, backupName) {
  if (!confirm(`هل أنت متأكد من استعادة النسخة الاحتياطية "${backupName}"؟\n\nتحذير: سيتم استبدال البيانات الحالية!`)) {
    return;
  }
  
  showProgress('استعادة النسخة الاحتياطية', 'جاري استعادة البيانات...');
  
  fetch('/api/backup/restore', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      backup_file: backupFile
    })
  })
  .then(response => response.json())
  .then(data => {
    hideProgress();
    
    if (data.success) {
      alert('تم استعادة النسخة الاحتياطية بنجاح!\n\nيُنصح بإعادة تشغيل التطبيق.');
    } else {
      alert('خطأ في استعادة النسخة الاحتياطية: ' + data.error);
    }
  })
  .catch(error => {
    hideProgress();
    console.error('خطأ:', error);
    alert('حدث خطأ في استعادة النسخة الاحتياطية');
  });
}

// حذف النسخة الاحتياطية
function deleteBackup(backupFile, backupName) {
  if (!confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${backupName}"؟\n\nلا يمكن التراجع عن هذا الإجراء!`)) {
    return;
  }
  
  // TODO: إضافة API endpoint لحذف النسخة الاحتياطية
  alert('ميزة حذف النسخ الاحتياطية ستكون متاحة قريباً');
}

// تحميل النسخة الاحتياطية
function downloadBackup(backupFile) {
  // TODO: إضافة API endpoint لتحميل النسخة الاحتياطية
  alert('ميزة تحميل النسخ الاحتياطية ستكون متاحة قريباً');
}

// تحديث قائمة النسخ الاحتياطية
function refreshBackups() {
  location.reload();
}

// إظهار شريط التقدم
function showProgress(title, text) {
  document.getElementById('progressTitle').textContent = title;
  document.getElementById('progressText').textContent = text;
  document.querySelector('.progress-container').style.display = 'block';
  
  // محاكاة التقدم
  let progress = 0;
  const progressBar = document.getElementById('progressBar');
  
  const interval = setInterval(() => {
    progress += Math.random() * 10;
    if (progress > 90) progress = 90;
    
    progressBar.style.width = progress + '%';
  }, 500);
  
  // حفظ المرجع لإيقافه لاحقاً
  window.progressInterval = interval;
}

// إخفاء شريط التقدم
function hideProgress() {
  if (window.progressInterval) {
    clearInterval(window.progressInterval);
  }
  
  document.getElementById('progressBar').style.width = '100%';
  
  setTimeout(() => {
    document.querySelector('.progress-container').style.display = 'none';
    document.getElementById('progressBar').style.width = '0%';
  }, 1000);
}

// تبديل وضع تعديل الإعدادات
function toggleConfig() {
  const inputs = document.querySelectorAll('#maxBackups, #compressBackups, #encryptBackups, #includeLogs');
  const actions = document.getElementById('configActions');
  
  inputs.forEach(input => {
    input.readOnly = false;
    input.disabled = false;
  });
  
  actions.style.display = 'block';
}

// حفظ الإعدادات
function saveConfig() {
  // TODO: إضافة API endpoint لحفظ إعدادات النسخ الاحتياطية
  alert('ميزة حفظ الإعدادات ستكون متاحة قريباً');
  cancelConfig();
}

// إلغاء تعديل الإعدادات
function cancelConfig() {
  const inputs = document.querySelectorAll('#maxBackups, #compressBackups, #encryptBackups, #includeLogs');
  const actions = document.getElementById('configActions');
  
  inputs.forEach(input => {
    input.readOnly = true;
    input.disabled = true;
  });
  
  actions.style.display = 'none';
  
  // إعادة تحميل الصفحة لاستعادة القيم الأصلية
  location.reload();
}
</script>
{% endblock %}