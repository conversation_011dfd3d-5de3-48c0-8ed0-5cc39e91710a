<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>فحص تكامل البيانات - نظام إدارة السائقين</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">نظام إدارة السائقين</a>
    <div class="d-flex">
      <a href="{{ url_for('dashboard') }}" class="btn btn-light me-2">السائقون</a>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('analytics') }}" class="btn btn-outline-light me-2">الإحصائيات</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light me-2">الإعدادات</a>
      <a href="{{ url_for('logout') }}" class="btn btn-outline-light">خروج</a>
    </div>
  </div>
</nav>

<div class="container mt-4">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">فحص تكامل البيانات</h4>
    <div>
      <a href="{{ url_for('data_integrity') }}" class="btn btn-primary me-2">إعادة فحص</a>
      {% if issues %}
        <a href="{{ url_for('data_integrity', fix='true') }}" class="btn btn-warning" onclick="return confirm('هل تريد إصلاح جميع المشاكل التي يمكن إصلاحها تلقائياً؟')">إصلاح تلقائي</a>
      {% endif %}
    </div>
  </div>

  {% if not issues %}
    <div class="alert alert-success" role="alert">
      <i class="fas fa-check-circle me-2"></i>
      <strong>ممتاز!</strong> لا توجد مشاكل في تكامل البيانات.
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>تنبيه:</strong> تم العثور على {{ issues|length }} مشكلة في البيانات.
    </div>

    {% for issue in issues %}
      <div class="card shadow-sm mb-3">
        <div class="card-body">
          {% if issue.type == 'balance_mismatch' %}
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-balance-scale text-warning me-2"></i>
              <h5 class="mb-0">عدم تطابق رصيد السائق</h5>
            </div>
            <p class="mb-2"><strong>السائق:</strong> {{ issue.driver }}</p>
            <div class="row">
              <div class="col-md-4">
                <small class="text-muted">الرصيد المسجل:</small>
                <div class="h6">{{ "%.2f"|format(issue.current_balance) }}</div>
              </div>
              <div class="col-md-4">
                <small class="text-muted">الرصيد المحسوب:</small>
                <div class="h6">{{ "%.2f"|format(issue.calculated_balance) }}</div>
              </div>
              <div class="col-md-4">
                <small class="text-muted">الفرق:</small>
                <div class="h6 {% if issue.difference > 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ "%.2f"|format(issue.difference) }}
                </div>
              </div>
            </div>
            <small class="text-muted">يمكن إصلاح هذه المشكلة تلقائياً بتحديث الرصيد المسجل ليطابق الرصيد المحسوب من المدفوعات.</small>

          {% elif issue.type == 'orphaned_vehicles' %}
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-car text-info me-2"></i>
              <h5 class="mb-0">مركبات بدون سائق</h5>
            </div>
            <p class="mb-2">عدد المركبات: <strong>{{ issue.count }}</strong></p>
            <div class="mb-2">
              <small class="text-muted">أرقام اللوحات:</small>
              <div>
                {% for plate in issue.vehicles %}
                  <span class="badge bg-secondary me-1">{{ plate }}</span>
                {% endfor %}
              </div>
            </div>
            <small class="text-muted">هذه المركبات غير مخصصة لأي سائق. يجب تخصيصها أو حذفها يدوياً.</small>

          {% elif issue.type == 'orphaned_payments' %}
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-money-bill text-danger me-2"></i>
              <h5 class="mb-0">مدفوعات بدون سائق</h5>
            </div>
            <p class="mb-2">عدد المدفوعات: <strong>{{ issue.count }}</strong></p>
            <div class="mb-2">
              <small class="text-muted">معرفات المدفوعات:</small>
              <div>
                {% for payment_id in issue.payment_ids %}
                  <span class="badge bg-danger me-1">#{{ payment_id }}</span>
                {% endfor %}
              </div>
            </div>
            <small class="text-muted">هذه المدفوعات مرتبطة بسائقين محذوفين. يجب حذفها أو إعادة ربطها يدوياً.</small>

          {% elif issue.type == 'orphaned_expenses' %}
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-receipt text-warning me-2"></i>
              <h5 class="mb-0">مصروفات بدون سائق أو مركبة</h5>
            </div>
            <p class="mb-2">عدد المصروفات: <strong>{{ issue.count }}</strong></p>
            <div class="mb-2">
              <small class="text-muted">معرفات المصروفات:</small>
              <div>
                {% for expense_id in issue.expense_ids %}
                  <span class="badge bg-warning me-1">#{{ expense_id }}</span>
                {% endfor %}
              </div>
            </div>
            <small class="text-muted">هذه المصروفات غير مرتبطة بأي سائق أو مركبة. يجب ربطها أو حذفها يدوياً.</small>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  {% endif %}

  <!-- معلومات إضافية -->
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">نصائح لصيانة البيانات</h5>
      <ul class="list-unstyled">
        <li class="mb-2">
          <i class="fas fa-lightbulb text-warning me-2"></i>
          قم بفحص تكامل البيانات بانتظام للتأكد من صحة الأرصدة والروابط.
        </li>
        <li class="mb-2">
          <i class="fas fa-shield-alt text-success me-2"></i>
          احتفظ بنسخ احتياطية منتظمة من قاعدة البيانات.
        </li>
        <li class="mb-2">
          <i class="fas fa-exclamation-circle text-info me-2"></i>
          تجنب حذف السائقين الذين لديهم مدفوعات أو مصروفات مرتبطة.
        </li>
        <li class="mb-2">
          <i class="fas fa-sync-alt text-primary me-2"></i>
          استخدم الإصلاح التلقائي بحذر وتأكد من النتائج.
        </li>
      </ul>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>