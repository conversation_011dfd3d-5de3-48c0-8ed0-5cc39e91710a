#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة السائقين والمركبات
Driver Management System Runner

يستخدم هذا الملف لتشغيل النظام في بيئات مختلفة:
- التطوير (Development)
- الإنتاج (Production)
- الاختبار (Testing)
"""

import os
import sys
import argparse
from app import app, db, init_db

def run_development():
    """تشغيل النظام في بيئة التطوير"""
    print("🚀 تشغيل النظام في بيئة التطوير...")
    print("📍 الرابط: http://localhost:5000")
    print("🔧 وضع التطوير: مفعل")
    print("🔄 إعادة التحميل التلقائي: مفعل")
    print("-" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True,
        threaded=True
    )

def run_production():
    """تشغيل النظام في بيئة الإنتاج"""
    try:
        import gunicorn
        print("🚀 تشغيل النظام في بيئة الإنتاج...")
        print("📍 الرابط: http://localhost:8000")
        print("🔧 وضع الإنتاج: مفعل")
        print("⚡ Gunicorn: مفعل")
        print("-" * 50)
        
        os.system("gunicorn --bind 0.0.0.0:8000 --workers 4 --timeout 120 wsgi:application")
    except ImportError:
        print("❌ Gunicorn غير مثبت. يرجى تثبيته أولاً:")
        print("pip install gunicorn")
        sys.exit(1)

def run_testing():
    """تشغيل النظام في بيئة الاختبار"""
    print("🧪 تشغيل النظام في بيئة الاختبار...")
    print("📍 الرابط: http://localhost:5001")
    print("🔧 وضع الاختبار: مفعل")
    print("-" * 50)
    
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    
    app.run(
        host='0.0.0.0',
        port=5001,
        debug=False,
        use_reloader=False
    )

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️  إعداد قاعدة البيانات...")
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        print("✅ تم إنشاء الجداول بنجاح")
        
        # إنشاء بيانات افتراضية
        init_db()
        print("✅ تم إنشاء البيانات الافتراضية بنجاح")
    
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy',
        'openpyxl',
        'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print("\n❌ المتطلبات المفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 لتثبيت المتطلبات المفقودة:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("\n✅ جميع المتطلبات متوفرة!")
    return True

def show_system_info():
    """عرض معلومات النظام"""
    print("ℹ️  معلومات النظام:")
    print(f"   🐍 Python: {sys.version}")
    print(f"   💻 نظام التشغيل: {os.name}")
    print(f"   📁 مجلد العمل: {os.getcwd()}")
    print(f"   🌐 Flask: {app.config.get('ENV', 'غير محدد')}")
    print("-" * 50)

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description='نظام إدارة السائقين والمركبات',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python run.py --dev                 # تشغيل في بيئة التطوير
  python run.py --prod                # تشغيل في بيئة الإنتاج
  python run.py --test                # تشغيل في بيئة الاختبار
  python run.py --setup               # إعداد قاعدة البيانات
  python run.py --check               # فحص المتطلبات
  python run.py --info                # عرض معلومات النظام
        """
    )
    
    parser.add_argument('--dev', action='store_true', help='تشغيل في بيئة التطوير')
    parser.add_argument('--prod', action='store_true', help='تشغيل في بيئة الإنتاج')
    parser.add_argument('--test', action='store_true', help='تشغيل في بيئة الاختبار')
    parser.add_argument('--setup', action='store_true', help='إعداد قاعدة البيانات')
    parser.add_argument('--check', action='store_true', help='فحص المتطلبات')
    parser.add_argument('--info', action='store_true', help='عرض معلومات النظام')
    
    args = parser.parse_args()
    
    # عرض الترحيب
    print("=" * 60)
    print("🚗 نظام إدارة السائقين والمركبات")
    print("   Driver Management System")
    print("=" * 60)
    
    if args.info:
        show_system_info()
    elif args.check:
        check_requirements()
    elif args.setup:
        setup_database()
    elif args.dev:
        if check_requirements():
            run_development()
    elif args.prod:
        if check_requirements():
            run_production()
    elif args.test:
        if check_requirements():
            run_testing()
    else:
        # الوضع الافتراضي - التطوير
        print("🔧 لم يتم تحديد وضع التشغيل، سيتم التشغيل في وضع التطوير")
        print("💡 استخدم --help لعرض جميع الخيارات المتاحة")
        print()
        if check_requirements():
            run_development()

if __name__ == "__main__":
    main()