{% extends "base.html" %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-users"></i> إدارة المستخدمين</h2>
        <div>
          <a href="{{ url_for('user_management.create_user') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مستخدم
          </a>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- شريط البحث والفلاتر -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <form method="GET" class="row g-3">
            <div class="col-md-6">
              <input type="text" class="form-control" name="search" 
                     value="{{ search }}" placeholder="البحث في المستخدمين...">
            </div>
            <div class="col-md-3">
              <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i> بحث
              </button>
              <a href="{{ url_for('user_management.users_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-times"></i> مسح
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات سريعة -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-primary">{{ users_data.pagination.total }}</h4>
          <small class="text-muted">إجمالي المستخدمين</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-success">{{ users_data.users|selectattr('is_active')|list|length }}</h4>
          <small class="text-muted">مستخدمين نشطين</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-warning">{{ users_data.users|rejectattr('is_active')|list|length }}</h4>
          <small class="text-muted">مستخدمين معطلين</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h4 class="text-info">{{ roles|length }}</h4>
          <small class="text-muted">الأدوار المتاحة</small>
        </div>
      </div>
    </div>
  </div>

  <!-- جدول المستخدمين -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-list"></i> قائمة المستخدمين</h6>
        </div>
        <div class="card-body">
          {% if users_data.users %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>المعرف</th>
                    <th>اسم المستخدم</th>
                    <th>الاسم الكامل</th>
                    <th>البريد الإلكتروني</th>
                    <th>الدور</th>
                    <th>الحالة</th>
                    <th>آخر دخول</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {% for user in users_data.users %}
                  <tr>
                    <td>{{ user.id }}</td>
                    <td>
                      <strong>{{ user.username }}</strong>
                      {% if user.username == 'admin' %}
                        <span class="badge bg-danger ms-1">مدير رئيسي</span>
                      {% endif %}
                    </td>
                    <td>{{ user.full_name or '-' }}</td>
                    <td>{{ user.email or '-' }}</td>
                    <td>
                      <span class="badge bg-info">{{ user.role_name }}</span>
                    </td>
                    <td>
                      {% if user.is_active %}
                        <span class="badge bg-success">نشط</span>
                      {% else %}
                        <span class="badge bg-secondary">معطل</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if user.last_login %}
                        {{ moment(user.last_login).format('YYYY-MM-DD HH:mm') if moment else user.last_login[:16] }}
                      {% else %}
                        <span class="text-muted">لم يسجل دخول</span>
                      {% endif %}
                    </td>
                    <td>
                      {{ moment(user.created_at).format('YYYY-MM-DD') if moment else user.created_at[:10] }}
                    </td>
                    <td>
                      <div class="btn-group btn-group-sm">
                        <a href="{{ url_for('user_management.edit_user', user_id=user.id) }}" 
                           class="btn btn-outline-primary" title="تعديل">
                          <i class="fas fa-edit"></i>
                        </a>
                        {% if user.username != 'admin' %}
                          <button onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})" 
                                  class="btn btn-outline-warning" 
                                  title="{{ 'تعطيل' if user.is_active else 'تفعيل' }}">
                            <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                          </button>
                          <button onclick="deleteUser({{ user.id }}, '{{ user.username }}')" 
                                  class="btn btn-outline-danger" title="حذف">
                            <i class="fas fa-trash"></i>
                          </button>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            {% if users_data.pagination.pages > 1 %}
            <nav aria-label="صفحات المستخدمين">
              <ul class="pagination justify-content-center">
                {% if users_data.pagination.has_prev %}
                  <li class="page-item">
                    <a class="page-link" href="{{ url_for('user_management.users_list', page=users_data.pagination.page-1, search=search) }}">السابق</a>
                  </li>
                {% endif %}
                
                {% for page_num in range(1, users_data.pagination.pages + 1) %}
                  {% if page_num == users_data.pagination.page %}
                    <li class="page-item active">
                      <span class="page-link">{{ page_num }}</span>
                    </li>
                  {% else %}
                    <li class="page-item">
                      <a class="page-link" href="{{ url_for('user_management.users_list', page=page_num, search=search) }}">{{ page_num }}</a>
                    </li>
                  {% endif %}
                {% endfor %}
                
                {% if users_data.pagination.has_next %}
                  <li class="page-item">
                    <a class="page-link" href="{{ url_for('user_management.users_list', page=users_data.pagination.page+1, search=search) }}">التالي</a>
                  </li>
                {% endif %}
              </ul>
            </nav>
            {% endif %}

          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-users fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">لا توجد مستخدمين</h5>
              {% if search %}
                <p class="text-muted">لم يتم العثور على مستخدمين يطابقون البحث "{{ search }}"</p>
                <a href="{{ url_for('user_management.users_list') }}" class="btn btn-outline-primary">
                  <i class="fas fa-times"></i> مسح البحث
                </a>
              {% else %}
                <p class="text-muted">قم بإضافة أول مستخدم للبدء</p>
                <a href="{{ url_for('user_management.create_user') }}" class="btn btn-primary">
                  <i class="fas fa-plus"></i> إضافة مستخدم
                </a>
              {% endif %}
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// تبديل حالة المستخدم (تفعيل/تعطيل)
function toggleUserStatus(userId, isActive) {
  const action = isActive ? 'تعطيل' : 'تفعيل';
  
  if (confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
    fetch(`/admin/users/${userId}/edit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        is_active: !isActive
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.error);
      }
    })
    .catch(error => {
      console.error('خطأ:', error);
      alert('حدث خطأ في تحديث حالة المستخدم');
    });
  }
}

// حذف المستخدم
function deleteUser(userId, username) {
  if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟\n\nلا يمكن التراجع عن هذا الإجراء!`)) {
    fetch(`/admin/users/${userId}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.error);
      }
    })
    .catch(error => {
      console.error('خطأ:', error);
      alert('حدث خطأ في حذف المستخدم');
    });
  }
}
</script>
{% endblock %}