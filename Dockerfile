# Dockerfile لنظام إدارة السائقين والمركبات
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# إنشاء مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيتها
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# إنشاء مجلدات البيانات
RUN mkdir -p /app/exports /app/backups

# إنشاء مستخدم غير جذر
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# تعريض المنفذ
EXPOSE 5000

# تهيئة قاعدة البيانات
RUN python init_db.py init

# تشغيل التطبيق
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "wsgi:application"]