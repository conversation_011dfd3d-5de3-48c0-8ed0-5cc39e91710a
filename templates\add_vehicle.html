<!-- templates/add_vehicle.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>إضافة مركبة</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <a href="{{ url_for('vehicles') }}" class="btn btn-light me-2">المركبات</a>
      <a href="{{ url_for('dashboard') }}" class="btn btn-outline-light me-2">السائقون</a>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light">الإعدادات</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="card shadow-sm">
    <div class="card-body">
      <h4 class="mb-3">إضافة مركبة جديدة</h4>
      
      <form method="post">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="plate_number" class="form-label">رقم اللوحة *</label>
            <input type="text" class="form-control" id="plate_number" name="plate_number" required>
          </div>
          <div class="col-md-6 mb-3">
            <label for="model" class="form-label">الموديل</label>
            <input type="text" class="form-control" id="model" name="model">
          </div>
        </div>
        
        <div class="mb-3">
          <label for="driver_id" class="form-label">السائق</label>
          <select class="form-select" id="driver_id" name="driver_id">
            <option value="">اختر السائق (اختياري)</option>
            {% for driver in drivers %}
              <option value="{{ driver.id }}">{{ driver.name }} - {{ driver.phone }}</option>
            {% endfor %}
          </select>
        </div>
        
        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary">إضافة المركبة</button>
          <a href="{{ url_for('vehicles') }}" class="btn btn-secondary">إلغاء</a>
        </div>
      </form>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>