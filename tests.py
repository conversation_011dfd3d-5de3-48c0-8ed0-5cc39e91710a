#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات نظام إدارة السائقين والمركبات
Driver Management System Tests

يحتوي هذا الملف على اختبارات شاملة للنظام:
- اختبارات النماذج (Models)
- اختبارات المسارات (Routes)
- اختبارات الوظائف (Functions)
- اختبارات التكامل (Integration)
"""

import unittest
import tempfile
import os
import json
from datetime import datetime, timedelta

# إعداد مسار التطبيق
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Driver, Vehicle, Payment, Expense, Salary

class DriverManagementTestCase(unittest.TestCase):
    """فئة الاختبارات الأساسية"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.db_fd, app.config['DATABASE'] = tempfile.mkstemp()
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        
        self.app = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        db.create_all()
        
        # إنشاء مستخدم تجريبي
        with self.app.session_transaction() as sess:
            sess['user_id'] = 1
            sess['username'] = 'admin'
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(app.config['DATABASE'])

class ModelTests(DriverManagementTestCase):
    """اختبارات النماذج"""
    
    def test_driver_creation(self):
        """اختبار إنشاء سائق"""
        driver = Driver(name="أحمد محمد", phone="**********", balance=1000.0)
        db.session.add(driver)
        db.session.commit()
        
        self.assertEqual(driver.name, "أحمد محمد")
        self.assertEqual(driver.phone, "**********")
        self.assertEqual(driver.balance, 1000.0)
        self.assertIsNotNone(driver.created_at)
    
    def test_driver_balance_calculation(self):
        """اختبار حساب رصيد السائق"""
        driver = Driver(name="محمد أحمد", phone="0507654321", balance=0.0)
        db.session.add(driver)
        db.session.commit()
        
        # إضافة مدفوعات
        payment1 = Payment(driver_id=driver.id, amount=500.0, direction='in', method='نقد')
        payment2 = Payment(driver_id=driver.id, amount=200.0, direction='out', method='تحويل')
        
        db.session.add(payment1)
        db.session.add(payment2)
        db.session.commit()
        
        # حساب الرصيد المتوقع
        expected_balance = 500.0 - 200.0
        
        # التحقق من المدفوعات
        self.assertEqual(len(driver.payments), 2)
    
    def test_vehicle_creation(self):
        """اختبار إنشاء مركبة"""
        driver = Driver(name="سائق تجريبي", phone="0501111111", balance=0.0)
        db.session.add(driver)
        db.session.commit()
        
        vehicle = Vehicle(
            driver_id=driver.id,
            plate_number="أ ب ج 1234",
            vehicle_type="سيارة",
            model="كامري",
            year=2020
        )
        db.session.add(vehicle)
        db.session.commit()
        
        self.assertEqual(vehicle.plate_number, "أ ب ج 1234")
        self.assertEqual(vehicle.driver_id, driver.id)
        self.assertEqual(len(driver.vehicles), 1)
    
    def test_payment_creation(self):
        """اختبار إنشاء مدفوعة"""
        driver = Driver(name="سائق تجريبي", phone="**********", balance=0.0)
        db.session.add(driver)
        db.session.commit()
        
        payment = Payment(
            driver_id=driver.id,
            amount=750.0,
            direction='in',
            method='نقد',
            note='مدفوعة تجريبية'
        )
        db.session.add(payment)
        db.session.commit()
        
        self.assertEqual(payment.amount, 750.0)
        self.assertEqual(payment.direction, 'in')
        self.assertEqual(payment.driver_id, driver.id)
    
    def test_expense_creation(self):
        """اختبار إنشاء مصروف"""
        driver = Driver(name="سائق تجريبي", phone="0503333333", balance=0.0)
        db.session.add(driver)
        db.session.commit()
        
        expense = Expense(
            driver_id=driver.id,
            amount=150.0,
            category='وقود',
            note='تعبئة وقود',
            occurred_at=datetime.now()
        )
        db.session.add(expense)
        db.session.commit()
        
        self.assertEqual(expense.amount, 150.0)
        self.assertEqual(expense.category, 'وقود')
        self.assertEqual(expense.driver_id, driver.id)

class RouteTests(DriverManagementTestCase):
    """اختبارات المسارات"""
    
    def test_dashboard_access(self):
        """اختبار الوصول للوحة التحكم"""
        response = self.app.get('/dashboard')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'dashboard', response.data)
    
    def test_add_driver_get(self):
        """اختبار صفحة إضافة سائق (GET)"""
        response = self.app.get('/drivers/add')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'form', response.data)
    
    def test_add_driver_post(self):
        """اختبار إضافة سائق (POST)"""
        response = self.app.post('/drivers/add', data={
            'name': 'سائق جديد',
            'phone': '0504444444',
            'balance': '500'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # التحقق من إضافة السائق في قاعدة البيانات
        driver = Driver.query.filter_by(name='سائق جديد').first()
        self.assertIsNotNone(driver)
        self.assertEqual(driver.phone, '0504444444')
        self.assertEqual(driver.balance, 500.0)
    
    def test_add_payment_post(self):
        """اختبار إضافة مدفوعة"""
        # إنشاء سائق أولاً
        driver = Driver(name="سائق للاختبار", phone="0505555555", balance=0.0)
        db.session.add(driver)
        db.session.commit()
        
        response = self.app.post('/payments/add', data={
            'driver_id': driver.id,
            'amount': '300',
            'direction': 'in',
            'method': 'نقد',
            'note': 'مدفوعة اختبار'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        
        # التحقق من إضافة المدفوعة
        payment = Payment.query.filter_by(driver_id=driver.id).first()
        self.assertIsNotNone(payment)
        self.assertEqual(payment.amount, 300.0)
    
    def test_search_drivers(self):
        """اختبار البحث في السائقين"""
        # إنشاء سائقين للاختبار
        driver1 = Driver(name="أحمد محمد", phone="0506666666", balance=0.0)
        driver2 = Driver(name="محمد أحمد", phone="0507777777", balance=0.0)
        db.session.add(driver1)
        db.session.add(driver2)
        db.session.commit()
        
        # البحث بالاسم
        response = self.app.get('/dashboard?search=أحمد')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'ahmed', response.data.lower())
    
    def test_export_drivers(self):
        """اختبار تصدير السائقين"""
        # إنشاء سائق للاختبار
        driver = Driver(name="سائق للتصدير", phone="0508888888", balance=100.0)
        db.session.add(driver)
        db.session.commit()
        
        response = self.app.get('/export/drivers')
        # يجب أن يكون التصدير ناجحاً أو يعطي رسالة خطأ مفهومة
        self.assertIn(response.status_code, [200, 302])

class IntegrationTests(DriverManagementTestCase):
    """اختبارات التكامل"""
    
    def test_driver_payment_workflow(self):
        """اختبار سير عمل السائق والمدفوعات"""
        # 1. إنشاء سائق
        response = self.app.post('/drivers/add', data={
            'name': 'سائق متكامل',
            'phone': '0509999999',
            'balance': '0'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        driver = Driver.query.filter_by(name='سائق متكامل').first()
        self.assertIsNotNone(driver)
        
        # 2. إضافة مدفوعة داخلة
        response = self.app.post('/payments/add', data={
            'driver_id': driver.id,
            'amount': '1000',
            'direction': 'in',
            'method': 'نقد'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 3. إضافة مدفوعة خارجة
        response = self.app.post('/payments/add', data={
            'driver_id': driver.id,
            'amount': '300',
            'direction': 'out',
            'method': 'تحويل'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # 4. التحقق من المدفوعات
        payments = Payment.query.filter_by(driver_id=driver.id).all()
        self.assertEqual(len(payments), 2)
        
        # 5. التحقق من الرصيد (يجب حسابه يدوياً في التطبيق)
        total_in = sum(p.amount for p in payments if p.direction == 'in')
        total_out = sum(p.amount for p in payments if p.direction == 'out')
        expected_balance = total_in - total_out
        self.assertEqual(expected_balance, 700.0)
    
    def test_vehicle_management(self):
        """اختبار إدارة المركبات"""
        # إنشاء سائق
        driver = Driver(name="سائق مركبات", phone="0501010101", balance=0.0)
        db.session.add(driver)
        db.session.commit()
        
        # إضافة مركبة
        response = self.app.post('/vehicles/add', data={
            'driver_id': driver.id,
            'plate_number': 'أ ب ج 5678',
            'vehicle_type': 'شاحنة',
            'model': 'هيونداي',
            'year': '2019'
        }, follow_redirects=True)
        
        # التحقق من إضافة المركبة
        vehicle = Vehicle.query.filter_by(driver_id=driver.id).first()
        if vehicle:  # إذا كان المسار موجود
            self.assertEqual(vehicle.plate_number, 'أ ب ج 5678')
            self.assertEqual(vehicle.vehicle_type, 'شاحنة')

class APITests(DriverManagementTestCase):
    """اختبارات API"""
    
    def test_stats_api(self):
        """اختبار API الإحصائيات"""
        # إنشاء بيانات تجريبية
        driver1 = Driver(name="سائق 1", phone="0501111111", balance=500.0)
        driver2 = Driver(name="سائق 2", phone="**********", balance=-200.0)
        db.session.add(driver1)
        db.session.add(driver2)
        db.session.commit()
        
        response = self.app.get('/api/stats')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['drivers_count'], 2)
        self.assertEqual(data['positive_balance_count'], 1)
        self.assertEqual(data['negative_balance_count'], 1)
    
    def test_health_check(self):
        """اختبار فحص صحة النظام"""
        response = self.app.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
        self.assertEqual(data['database'], 'connected')

class SecurityTests(DriverManagementTestCase):
    """اختبارات الأمان"""
    
    def test_login_required(self):
        """اختبار متطلب تسجيل الدخول"""
        # محاولة الوصول بدون تسجيل دخول
        with app.test_client() as client:
            response = client.get('/dashboard')
            # يجب إعادة توجيه لصفحة تسجيل الدخول
            self.assertIn(response.status_code, [302, 401])
    
    def test_sql_injection_protection(self):
        """اختبار الحماية من SQL Injection"""
        malicious_input = "'; DROP TABLE drivers; --"
        
        response = self.app.post('/drivers/add', data={
            'name': malicious_input,
            'phone': '**********',
            'balance': '0'
        }, follow_redirects=True)
        
        # يجب أن يتم التعامل مع الإدخال بأمان
        # والتحقق من أن الجداول لا تزال موجودة
        drivers = Driver.query.all()
        # إذا كان هناك سائقين، فالحماية تعمل
        self.assertIsInstance(drivers, list)

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء تشغيل الاختبارات...")
    print("=" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات النماذج
    test_suite.addTest(unittest.makeSuite(ModelTests))
    
    # إضافة اختبارات المسارات
    test_suite.addTest(unittest.makeSuite(RouteTests))
    
    # إضافة اختبارات التكامل
    test_suite.addTest(unittest.makeSuite(IntegrationTests))
    
    # إضافة اختبارات API
    test_suite.addTest(unittest.makeSuite(APITests))
    
    # إضافة اختبارات الأمان
    test_suite.addTest(unittest.makeSuite(SecurityTests))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    print(f"✅ نجح: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ فشل: {len(result.failures)}")
    print(f"🚫 أخطاء: {len(result.errors)}")
    print(f"📈 المجموع: {result.testsRun}")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"   - {test}")
    
    if result.errors:
        print("\n🚫 أخطاء الاختبارات:")
        for test, traceback in result.errors:
            print(f"   - {test}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n🎯 معدل النجاح: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)