#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء والتنبيهات
Performance Monitoring and Alerting System

يراقب:
- أداء النظام
- استخدام الذاكرة
- مساحة القرص
- أوقات الاستجابة
- أخطاء النظام
"""

import psutil
import os
import time
import json
import logging
from datetime import datetime, timedelta
from threading import Thread
import sqlite3
from collections import deque

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemMonitor:
    """مراقب النظام"""
    
    def __init__(self, app=None):
        self.app = app
        self.running = False
        self.metrics = deque(maxlen=1000)  # آخر 1000 قياس
        self.alerts = []
        
        # حدود التنبيهات
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0,
            'response_time': 5.0,  # ثواني
            'error_rate': 10.0     # أخطاء في الدقيقة
        }
        
        # إنشاء مجلد المراقبة
        os.makedirs('monitoring', exist_ok=True)
        
        # إعداد قاعدة بيانات المراقبة
        self.setup_monitoring_db()
    
    def setup_monitoring_db(self):
        """إعداد قاعدة بيانات المراقبة"""
        try:
            conn = sqlite3.connect('monitoring/metrics.db')
            cursor = conn.cursor()
            
            # جدول المقاييس
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    cpu_percent REAL,
                    memory_percent REAL,
                    disk_percent REAL,
                    network_sent INTEGER,
                    network_recv INTEGER,
                    active_connections INTEGER,
                    response_time REAL
                )
            ''')
            
            # جدول التنبيهات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    alert_type TEXT,
                    severity TEXT,
                    message TEXT,
                    value REAL,
                    threshold REAL,
                    resolved BOOLEAN DEFAULT FALSE
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("تم إعداد قاعدة بيانات المراقبة")
            
        except Exception as e:
            logger.error(f"خطأ في إعداد قاعدة بيانات المراقبة: {e}")
    
    def collect_system_metrics(self):
        """جمع مقاييس النظام"""
        try:
            # معلومات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # معلومات القرص
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # معلومات الشبكة
            network = psutil.net_io_counters()
            network_sent = network.bytes_sent
            network_recv = network.bytes_recv
            
            # عدد الاتصالات النشطة
            connections = len(psutil.net_connections())
            
            # قياس وقت الاستجابة (محاكاة)
            response_time = self.measure_response_time()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'network_sent': network_sent,
                'network_recv': network_recv,
                'active_connections': connections,
                'response_time': response_time
            }
            
            # حفظ في الذاكرة
            self.metrics.append(metrics)
            
            # حفظ في قاعدة البيانات
            self.save_metrics_to_db(metrics)
            
            # فحص التنبيهات
            self.check_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"خطأ في جمع مقاييس النظام: {e}")
            return None
    
    def measure_response_time(self):
        """قياس وقت استجابة التطبيق"""
        try:
            if self.app:
                with self.app.test_client() as client:
                    start_time = time.time()
                    response = client.get('/health')
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        return end_time - start_time
            
            return 0.0
            
        except Exception as e:
            logger.error(f"خطأ في قياس وقت الاستجابة: {e}")
            return 0.0
    
    def save_metrics_to_db(self, metrics):
        """حفظ المقاييس في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('monitoring/metrics.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO metrics (
                    cpu_percent, memory_percent, disk_percent,
                    network_sent, network_recv, active_connections, response_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                metrics['cpu_percent'],
                metrics['memory_percent'],
                metrics['disk_percent'],
                metrics['network_sent'],
                metrics['network_recv'],
                metrics['active_connections'],
                metrics['response_time']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ المقاييس: {e}")
    
    def check_alerts(self, metrics):
        """فحص التنبيهات"""
        alerts = []
        
        # تنبيه المعالج
        if metrics['cpu_percent'] > self.thresholds['cpu_percent']:
            alert = {
                'type': 'cpu_high',
                'severity': 'warning',
                'message': f'استخدام المعالج مرتفع: {metrics["cpu_percent"]:.1f}%',
                'value': metrics['cpu_percent'],
                'threshold': self.thresholds['cpu_percent']
            }
            alerts.append(alert)
        
        # تنبيه الذاكرة
        if metrics['memory_percent'] > self.thresholds['memory_percent']:
            alert = {
                'type': 'memory_high',
                'severity': 'warning',
                'message': f'استخدام الذاكرة مرتفع: {metrics["memory_percent"]:.1f}%',
                'value': metrics['memory_percent'],
                'threshold': self.thresholds['memory_percent']
            }
            alerts.append(alert)
        
        # تنبيه القرص
        if metrics['disk_percent'] > self.thresholds['disk_percent']:
            alert = {
                'type': 'disk_high',
                'severity': 'critical',
                'message': f'مساحة القرص منخفضة: {metrics["disk_percent"]:.1f}%',
                'value': metrics['disk_percent'],
                'threshold': self.thresholds['disk_percent']
            }
            alerts.append(alert)
        
        # تنبيه وقت الاستجابة
        if metrics['response_time'] > self.thresholds['response_time']:
            alert = {
                'type': 'response_slow',
                'severity': 'warning',
                'message': f'وقت الاستجابة بطيء: {metrics["response_time"]:.2f} ثانية',
                'value': metrics['response_time'],
                'threshold': self.thresholds['response_time']
            }
            alerts.append(alert)
        
        # حفظ التنبيهات
        for alert in alerts:
            self.save_alert_to_db(alert)
            self.alerts.append(alert)
            logger.warning(f"تنبيه: {alert['message']}")
    
    def save_alert_to_db(self, alert):
        """حفظ التنبيه في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('monitoring/metrics.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts (alert_type, severity, message, value, threshold)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                alert['type'],
                alert['severity'],
                alert['message'],
                alert['value'],
                alert['threshold']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"خطأ في حفظ التنبيه: {e}")
    
    def get_system_status(self):
        """الحصول على حالة النظام"""
        if not self.metrics:
            return None
        
        latest = self.metrics[-1]
        
        # تحديد الحالة العامة
        status = 'healthy'
        if (latest['cpu_percent'] > self.thresholds['cpu_percent'] or
            latest['memory_percent'] > self.thresholds['memory_percent']):
            status = 'warning'
        
        if latest['disk_percent'] > self.thresholds['disk_percent']:
            status = 'critical'
        
        return {
            'status': status,
            'timestamp': latest['timestamp'],
            'cpu_percent': latest['cpu_percent'],
            'memory_percent': latest['memory_percent'],
            'disk_percent': latest['disk_percent'],
            'response_time': latest['response_time'],
            'active_connections': latest['active_connections']
        }
    
    def get_metrics_history(self, hours=24):
        """الحصول على تاريخ المقاييس"""
        try:
            conn = sqlite3.connect('monitoring/metrics.db')
            cursor = conn.cursor()
            
            since = datetime.now() - timedelta(hours=hours)
            
            cursor.execute('''
                SELECT * FROM metrics 
                WHERE timestamp > ? 
                ORDER BY timestamp DESC
            ''', (since.isoformat(),))
            
            rows = cursor.fetchall()
            conn.close()
            
            # تحويل إلى قاموس
            columns = ['id', 'timestamp', 'cpu_percent', 'memory_percent', 
                      'disk_percent', 'network_sent', 'network_recv', 
                      'active_connections', 'response_time']
            
            metrics = []
            for row in rows:
                metric = dict(zip(columns, row))
                metrics.append(metric)
            
            return metrics
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ المقاييس: {e}")
            return []
    
    def get_alerts_history(self, hours=24):
        """الحصول على تاريخ التنبيهات"""
        try:
            conn = sqlite3.connect('monitoring/metrics.db')
            cursor = conn.cursor()
            
            since = datetime.now() - timedelta(hours=hours)
            
            cursor.execute('''
                SELECT * FROM alerts 
                WHERE timestamp > ? 
                ORDER BY timestamp DESC
            ''', (since.isoformat(),))
            
            rows = cursor.fetchall()
            conn.close()
            
            # تحويل إلى قاموس
            columns = ['id', 'timestamp', 'alert_type', 'severity', 
                      'message', 'value', 'threshold', 'resolved']
            
            alerts = []
            for row in rows:
                alert = dict(zip(columns, row))
                alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ التنبيهات: {e}")
            return []
    
    def generate_report(self):
        """إنشاء تقرير المراقبة"""
        try:
            status = self.get_system_status()
            metrics_24h = self.get_metrics_history(24)
            alerts_24h = self.get_alerts_history(24)
            
            if not status:
                return None
            
            # حساب المتوسطات
            if metrics_24h:
                avg_cpu = sum(m['cpu_percent'] for m in metrics_24h) / len(metrics_24h)
                avg_memory = sum(m['memory_percent'] for m in metrics_24h) / len(metrics_24h)
                avg_response = sum(m['response_time'] for m in metrics_24h) / len(metrics_24h)
            else:
                avg_cpu = avg_memory = avg_response = 0
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'status': status,
                'current_metrics': {
                    'cpu_percent': status['cpu_percent'],
                    'memory_percent': status['memory_percent'],
                    'disk_percent': status['disk_percent'],
                    'response_time': status['response_time']
                },
                'averages_24h': {
                    'cpu_percent': avg_cpu,
                    'memory_percent': avg_memory,
                    'response_time': avg_response
                },
                'alerts_count_24h': len(alerts_24h),
                'critical_alerts': len([a for a in alerts_24h if a['severity'] == 'critical']),
                'warning_alerts': len([a for a in alerts_24h if a['severity'] == 'warning'])
            }
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير المراقبة: {e}")
            return None
    
    def cleanup_old_data(self, days=30):
        """تنظيف البيانات القديمة"""
        try:
            conn = sqlite3.connect('monitoring/metrics.db')
            cursor = conn.cursor()
            
            cutoff = datetime.now() - timedelta(days=days)
            
            # حذف المقاييس القديمة
            cursor.execute('DELETE FROM metrics WHERE timestamp < ?', (cutoff.isoformat(),))
            metrics_deleted = cursor.rowcount
            
            # حذف التنبيهات القديمة
            cursor.execute('DELETE FROM alerts WHERE timestamp < ?', (cutoff.isoformat(),))
            alerts_deleted = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            logger.info(f"تم حذف {metrics_deleted} مقياس و {alerts_deleted} تنبيه قديم")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {e}")
    
    def start_monitoring(self, interval=60):
        """بدء المراقبة"""
        self.running = True
        logger.info(f"بدء مراقبة النظام (كل {interval} ثانية)")
        
        def monitor_loop():
            while self.running:
                try:
                    self.collect_system_metrics()
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"خطأ في حلقة المراقبة: {e}")
                    time.sleep(interval)
        
        # تشغيل المراقبة في thread منفصل
        monitor_thread = Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        
        return monitor_thread
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.running = False
        logger.info("تم إيقاف مراقبة النظام")

# إنشاء مثيل مراقب النظام
system_monitor = SystemMonitor()

def init_monitoring(app):
    """تهيئة نظام المراقبة"""
    system_monitor.app = app
    return system_monitor