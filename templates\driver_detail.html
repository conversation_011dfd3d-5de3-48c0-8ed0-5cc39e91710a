<!-- templates/driver_detail.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>تفاصيل السائق</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <a href="{{ url_for('dashboard') }}" class="btn btn-light me-2">السائقون</a>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light">الإعدادات</a>
    </div>
  </div>
</nav>

<div class="container">
  <div class="card shadow-sm">
    <div class="card-body">
      <h4 class="mb-3">تفاصيل السائق</h4>
      <dl class="row">
        <dt class="col-sm-3">الرقم</dt>
        <dd class="col-sm-9">{{ driver.id }}</dd>
        <dt class="col-sm-3">الاسم</dt>
        <dd class="col-sm-9">{{ driver.name }}</dd>
        <dt class="col-sm-3">رقم الهاتف</dt>
        <dd class="col-sm-9">{{ driver.phone }}</dd>
        <dt class="col-sm-3">الرصيد</dt>
        <dd class="col-sm-9">
          <span class="h5 {% if driver.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
            {{ "%.2f"|format(driver.balance) }}
          </span>
        </dd>
        <dt class="col-sm-3">عدد المركبات</dt>
        <dd class="col-sm-9">{{ driver.vehicles|length }}</dd>
      </dl>
      
      <div class="mt-3">
        <a href="{{ url_for('edit_driver', driver_id=driver.id) }}" class="btn btn-warning">تعديل</a>
        <a href="{{ url_for('add_payment') }}?driver_id={{ driver.id }}" class="btn btn-success">إضافة مدفوعة</a>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">عودة</a>
      </div>
    </div>
  </div>

  <!-- المركبات -->
  {% if driver.vehicles %}
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">المركبات</h5>
      <div class="table-responsive">
        <table class="table table-sm">
          <thead>
            <tr>
              <th>رقم اللوحة</th>
              <th>الموديل</th>
            </tr>
          </thead>
          <tbody>
            {% for vehicle in driver.vehicles %}
            <tr>
              <td>{{ vehicle.plate_number }}</td>
              <td>{{ vehicle.model or '-' }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- آخر المدفوعات -->
  {% if recent_payments %}
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">آخر المدفوعات</h5>
      <div class="table-responsive">
        <table class="table table-sm">
          <thead>
            <tr>
              <th>المبلغ</th>
              <th>النوع</th>
              <th>الطريقة</th>
              <th>التاريخ</th>
              <th>ملاحظة</th>
            </tr>
          </thead>
          <tbody>
            {% for payment in recent_payments %}
            <tr>
              <td>
                {% if payment.direction == 'in' %}
                  <span class="text-success">+{{ "%.2f"|format(payment.amount) }}</span>
                {% else %}
                  <span class="text-danger">-{{ "%.2f"|format(payment.amount) }}</span>
                {% endif %}
              </td>
              <td>
                {% if payment.direction == 'in' %}
                  <span class="badge bg-success">دخل</span>
                {% else %}
                  <span class="badge bg-danger">خرج</span>
                {% endif %}
              </td>
              <td>{{ payment.method or '-' }}</td>
              <td>{{ payment.created_at.strftime('%Y-%m-%d') }}</td>
              <td>{{ payment.note or '-' }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="mt-2">
        <a href="{{ url_for('payments') }}" class="btn btn-sm btn-outline-primary">عرض جميع المدفوعات</a>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- آخر الرواتب -->
  {% if recent_salaries %}
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">آخر الرواتب</h5>
      <div class="table-responsive">
        <table class="table table-sm">
          <thead>
            <tr>
              <th>المبلغ</th>
              <th>فترة الراتب</th>
              <th>تاريخ الدفع</th>
            </tr>
          </thead>
          <tbody>
            {% for salary in recent_salaries %}
            <tr>
              <td>{{ "%.2f"|format(salary.amount) }}</td>
              <td>
                {% if salary.period_start and salary.period_end %}
                  {{ salary.period_start.strftime('%Y-%m-%d') }} - {{ salary.period_end.strftime('%Y-%m-%d') }}
                {% else %}
                  غير محدد
                {% endif %}
              </td>
              <td>{{ salary.paid_at.strftime('%Y-%m-%d') }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="mt-2">
        <a href="{{ url_for('salaries') }}" class="btn btn-sm btn-outline-primary">عرض جميع الرواتب</a>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- آخر المصروفات -->
  {% if recent_expenses %}
  <div class="card shadow-sm mt-4">
    <div class="card-body">
      <h5 class="mb-3">آخر المصروفات</h5>
      <div class="table-responsive">
        <table class="table table-sm">
          <thead>
            <tr>
              <th>المبلغ</th>
              <th>الفئة</th>
              <th>المركبة</th>
              <th>التاريخ</th>
              <th>ملاحظة</th>
            </tr>
          </thead>
          <tbody>
            {% for expense in recent_expenses %}
            <tr>
              <td>{{ "%.2f"|format(expense.amount) }}</td>
              <td>{{ expense.category or '-' }}</td>
              <td>{{ expense.vehicle.plate_number if expense.vehicle else '-' }}</td>
              <td>{{ expense.occurred_at.strftime('%Y-%m-%d') }}</td>
              <td>{{ expense.note or '-' }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      <div class="mt-2">
        <a href="{{ url_for('expenses') }}" class="btn btn-sm btn-outline-primary">عرض جميع المصروفات</a>
      </div>
    </div>
  </div>
</div>
</body>
</html>