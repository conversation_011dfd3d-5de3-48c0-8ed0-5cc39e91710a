<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الإحصائيات المتقدمة - نظام إدارة السائقين</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">نظام إدارة السائقين</a>
    <div class="d-flex">
      <div class="dropdown me-2">
        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
          الإدارة
        </button>
        <ul class="dropdown-menu">
          <li><a class="dropdown-item" href="{{ url_for('dashboard') }}">السائقون</a></li>
          <li><a class="dropdown-item" href="{{ url_for('vehicles') }}">المركبات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('payments') }}">المدفوعات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('salaries') }}">الرواتب</a></li>
          <li><a class="dropdown-item" href="{{ url_for('expenses') }}">المصروفات</a></li>
        </ul>
      </div>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('analytics') }}" class="btn btn-outline-light me-2 active">الإحصائيات</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light me-2">الإعدادات</a>
      <a href="{{ url_for('logout') }}" class="btn btn-outline-light">خروج</a>
    </div>
  </div>
</nav>

<div class="container mt-4">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <h4 class="mb-4">الإحصائيات المتقدمة</h4>

  <!-- إحصائيات شهرية -->
  <div class="row g-4 mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">المدفوعات الشهرية</h5>
          <canvas id="monthlyPaymentsChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">المصروفات الشهرية</h5>
          <canvas id="monthlyExpensesChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- توزيع الأرصدة -->
  <div class="row g-4 mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">توزيع أرصدة السائقين</h5>
          <canvas id="balanceDistributionChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">المصروفات حسب الفئة</h5>
          <canvas id="expenseCategoriesChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات تفصيلية -->
  <div class="row g-4">
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">أعلى 5 سائقين برصيد موجب</h5>
          <div class="list-group list-group-flush">
            {% for driver in top_positive_drivers %}
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>{{ driver.name }}</span>
                <span class="badge bg-success">{{ "%.2f"|format(driver.balance) }}</span>
              </div>
            {% else %}
              <div class="text-muted text-center">لا توجد بيانات</div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">أعلى 5 سائقين برصيد سالب</h5>
          <div class="list-group list-group-flush">
            {% for driver in top_negative_drivers %}
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>{{ driver.name }}</span>
                <span class="badge bg-danger">{{ "%.2f"|format(driver.balance) }}</span>
              </div>
            {% else %}
              <div class="text-muted text-center">لا توجد بيانات</div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-body">
          <h5 class="mb-3">أكثر السائقين نشاطاً</h5>
          <div class="list-group list-group-flush">
            {% for driver in most_active_drivers %}
              <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>{{ driver.name }}</span>
                <span class="badge bg-info">{{ driver.payment_count }} مدفوعة</span>
              </div>
            {% else %}
              <div class="text-muted text-center">لا توجد بيانات</div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// بيانات الرسوم البيانية
const monthlyPaymentsData = {{ monthly_payments_data|safe }};
const monthlyExpensesData = {{ monthly_expenses_data|safe }};
const balanceDistributionData = {{ balance_distribution_data|safe }};
const expenseCategoriesData = {{ expense_categories_data|safe }};

// رسم المدفوعات الشهرية
const monthlyPaymentsCtx = document.getElementById('monthlyPaymentsChart').getContext('2d');
new Chart(monthlyPaymentsCtx, {
    type: 'line',
    data: {
        labels: monthlyPaymentsData.labels,
        datasets: [{
            label: 'دخل',
            data: monthlyPaymentsData.income,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'خرج',
            data: monthlyPaymentsData.outcome,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// رسم المصروفات الشهرية
const monthlyExpensesCtx = document.getElementById('monthlyExpensesChart').getContext('2d');
new Chart(monthlyExpensesCtx, {
    type: 'bar',
    data: {
        labels: monthlyExpensesData.labels,
        datasets: [{
            label: 'المصروفات',
            data: monthlyExpensesData.amounts,
            backgroundColor: 'rgba(255, 159, 64, 0.8)',
            borderColor: 'rgba(255, 159, 64, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// رسم توزيع الأرصدة
const balanceDistributionCtx = document.getElementById('balanceDistributionChart').getContext('2d');
new Chart(balanceDistributionCtx, {
    type: 'doughnut',
    data: {
        labels: balanceDistributionData.labels,
        datasets: [{
            data: balanceDistributionData.values,
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});

// رسم المصروفات حسب الفئة
const expenseCategoriesCtx = document.getElementById('expenseCategoriesChart').getContext('2d');
new Chart(expenseCategoriesCtx, {
    type: 'pie',
    data: {
        labels: expenseCategoriesData.labels,
        datasets: [{
            data: expenseCategoriesData.values,
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>