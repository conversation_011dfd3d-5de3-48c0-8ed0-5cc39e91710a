<!-- templates/add_payment.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>إضافة مدفوعة</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <a href="{{ url_for('payments') }}" class="btn btn-light me-2">المدفوعات</a>
      <a href="{{ url_for('dashboard') }}" class="btn btn-outline-light me-2">السائقون</a>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light">الإعدادات</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="card shadow-sm">
    <div class="card-body">
      <h4 class="mb-3">إضافة مدفوعة جديدة</h4>
      
      <form method="post">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="driver_id" class="form-label">السائق *</label>
            <select class="form-select" id="driver_id" name="driver_id" required>
              <option value="">اختر السائق</option>
              {% for driver in drivers %}
                <option value="{{ driver.id }}" {% if selected_driver_id and selected_driver_id|int == driver.id %}selected{% endif %}>
                  {{ driver.name }} - {{ driver.phone }} (الرصيد: {{ "%.2f"|format(driver.balance) }})
                </option>
              {% endfor %}
            </select>
          </div>
          <div class="col-md-6 mb-3">
            <label for="amount" class="form-label">المبلغ *</label>
            <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="direction" class="form-label">نوع المدفوعة *</label>
            <select class="form-select" id="direction" name="direction" required>
              <option value="in">دخل (إضافة للرصيد)</option>
              <option value="out">خرج (خصم من الرصيد)</option>
            </select>
          </div>
          <div class="col-md-6 mb-3">
            <label for="method" class="form-label">طريقة الدفع</label>
            <select class="form-select" id="method" name="method">
              <option value="">اختر الطريقة</option>
              <option value="cash">نقداً</option>
              <option value="transfer">تحويل بنكي</option>
              <option value="check">شيك</option>
              <option value="other">أخرى</option>
            </select>
          </div>
        </div>
        
        <div class="mb-3">
          <label for="note" class="form-label">ملاحظة</label>
          <textarea class="form-control" id="note" name="note" rows="3"></textarea>
        </div>
        
        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary">إضافة المدفوعة</button>
          <a href="{{ url_for('payments') }}" class="btn btn-secondary">إلغاء</a>
        </div>
      </form>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>