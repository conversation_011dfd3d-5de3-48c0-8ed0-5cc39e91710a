#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين المتقدم
Advanced User Management System

يوفر:
- إدارة المستخدمين والأدوار
- صلاحيات مفصلة
- تسجيل العمليات
- إدارة الجلسات
- أمان متقدم
"""

from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash, session
from functools import wraps
from werkzeug.security import generate_password_hash, check_password_hash
from app import db, User, Role
import logging
from datetime import datetime, timedelta
import json

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إنشاء Blueprint لإدارة المستخدمين
user_mgmt_bp = Blueprint('user_management', __name__, url_prefix='/admin')

class Permission:
    """صلاحيات النظام"""
    # صلاحيات السائقين
    VIEW_DRIVERS = 'view_drivers'
    CREATE_DRIVERS = 'create_drivers'
    EDIT_DRIVERS = 'edit_drivers'
    DELETE_DRIVERS = 'delete_drivers'
    
    # صلاحيات المركبات
    VIEW_VEHICLES = 'view_vehicles'
    CREATE_VEHICLES = 'create_vehicles'
    EDIT_VEHICLES = 'edit_vehicles'
    DELETE_VEHICLES = 'delete_vehicles'
    
    # صلاحيات المدفوعات
    VIEW_PAYMENTS = 'view_payments'
    CREATE_PAYMENTS = 'create_payments'
    EDIT_PAYMENTS = 'edit_payments'
    DELETE_PAYMENTS = 'delete_payments'
    
    # صلاحيات المصروفات
    VIEW_EXPENSES = 'view_expenses'
    CREATE_EXPENSES = 'create_expenses'
    EDIT_EXPENSES = 'edit_expenses'
    DELETE_EXPENSES = 'delete_expenses'
    
    # صلاحيات التقارير
    VIEW_REPORTS = 'view_reports'
    EXPORT_REPORTS = 'export_reports'
    
    # صلاحيات النظام
    VIEW_SYSTEM_SETTINGS = 'view_system_settings'
    EDIT_SYSTEM_SETTINGS = 'edit_system_settings'
    VIEW_SYSTEM_LOGS = 'view_system_logs'
    MANAGE_BACKUPS = 'manage_backups'
    
    # صلاحيات إدارة المستخدمين
    VIEW_USERS = 'view_users'
    CREATE_USERS = 'create_users'
    EDIT_USERS = 'edit_users'
    DELETE_USERS = 'delete_users'
    MANAGE_ROLES = 'manage_roles'

class UserManager:
    """مدير المستخدمين"""
    
    def __init__(self, app=None):
        self.app = app
        self.default_roles = {
            'admin': {
                'name': 'مدير النظام',
                'description': 'صلاحيات كاملة لإدارة النظام',
                'permissions': [
                    Permission.VIEW_DRIVERS, Permission.CREATE_DRIVERS, Permission.EDIT_DRIVERS, Permission.DELETE_DRIVERS,
                    Permission.VIEW_VEHICLES, Permission.CREATE_VEHICLES, Permission.EDIT_VEHICLES, Permission.DELETE_VEHICLES,
                    Permission.VIEW_PAYMENTS, Permission.CREATE_PAYMENTS, Permission.EDIT_PAYMENTS, Permission.DELETE_PAYMENTS,
                    Permission.VIEW_EXPENSES, Permission.CREATE_EXPENSES, Permission.EDIT_EXPENSES, Permission.DELETE_EXPENSES,
                    Permission.VIEW_REPORTS, Permission.EXPORT_REPORTS,
                    Permission.VIEW_SYSTEM_SETTINGS, Permission.EDIT_SYSTEM_SETTINGS, Permission.VIEW_SYSTEM_LOGS, Permission.MANAGE_BACKUPS,
                    Permission.VIEW_USERS, Permission.CREATE_USERS, Permission.EDIT_USERS, Permission.DELETE_USERS, Permission.MANAGE_ROLES
                ]
            },
            'manager': {
                'name': 'مدير',
                'description': 'صلاحيات إدارية محدودة',
                'permissions': [
                    Permission.VIEW_DRIVERS, Permission.CREATE_DRIVERS, Permission.EDIT_DRIVERS,
                    Permission.VIEW_VEHICLES, Permission.CREATE_VEHICLES, Permission.EDIT_VEHICLES,
                    Permission.VIEW_PAYMENTS, Permission.CREATE_PAYMENTS, Permission.EDIT_PAYMENTS,
                    Permission.VIEW_EXPENSES, Permission.CREATE_EXPENSES, Permission.EDIT_EXPENSES,
                    Permission.VIEW_REPORTS, Permission.EXPORT_REPORTS
                ]
            },
            'operator': {
                'name': 'مشغل',
                'description': 'صلاحيات تشغيلية أساسية',
                'permissions': [
                    Permission.VIEW_DRIVERS, Permission.CREATE_DRIVERS, Permission.EDIT_DRIVERS,
                    Permission.VIEW_VEHICLES,
                    Permission.VIEW_PAYMENTS, Permission.CREATE_PAYMENTS,
                    Permission.VIEW_EXPENSES, Permission.CREATE_EXPENSES,
                    Permission.VIEW_REPORTS
                ]
            },
            'viewer': {
                'name': 'مشاهد',
                'description': 'صلاحيات عرض فقط',
                'permissions': [
                    Permission.VIEW_DRIVERS,
                    Permission.VIEW_VEHICLES,
                    Permission.VIEW_PAYMENTS,
                    Permission.VIEW_EXPENSES,
                    Permission.VIEW_REPORTS
                ]
            }
        }
    
    def create_default_roles(self):
        """إنشاء الأدوار الافتراضية"""
        try:
            for role_key, role_data in self.default_roles.items():
                existing_role = Role.query.filter_by(name=role_key).first()
                
                if not existing_role:
                    role = Role(
                        name=role_key,
                        display_name=role_data['name'],
                        description=role_data['description'],
                        permissions=json.dumps(role_data['permissions'])
                    )
                    db.session.add(role)
                    logger.info(f"تم إنشاء الدور: {role_data['name']}")
            
            db.session.commit()
            logger.info("تم إنشاء الأدوار الافتراضية")
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الأدوار الافتراضية: {e}")
            db.session.rollback()
    
    def create_user(self, username, password, email, role_name, full_name=None):
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم وجود المستخدم
            existing_user = User.query.filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                return {'success': False, 'error': 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'}
            
            # البحث عن الدور
            role = Role.query.filter_by(name=role_name).first()
            if not role:
                return {'success': False, 'error': 'الدور غير موجود'}
            
            # إنشاء المستخدم
            user = User(
                username=username,
                password=generate_password_hash(password),
                email=email,
                full_name=full_name or username,
                role_id=role.id,
                is_active=True,
                created_at=datetime.utcnow()
            )
            
            db.session.add(user)
            db.session.commit()
            
            logger.info(f"تم إنشاء المستخدم: {username}")
            
            return {
                'success': True,
                'message': 'تم إنشاء المستخدم بنجاح',
                'user_id': user.id
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء المستخدم: {e}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def update_user(self, user_id, **kwargs):
        """تحديث بيانات المستخدم"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'المستخدم غير موجود'}
            
            # تحديث البيانات
            for key, value in kwargs.items():
                if hasattr(user, key):
                    if key == 'password' and value:
                        user.password = generate_password_hash(value)
                    elif key == 'role_name' and value:
                        role = Role.query.filter_by(name=value).first()
                        if role:
                            user.role_id = role.id
                    else:
                        setattr(user, key, value)
            
            user.updated_at = datetime.utcnow()
            db.session.commit()
            
            logger.info(f"تم تحديث المستخدم: {user.username}")
            
            return {'success': True, 'message': 'تم تحديث المستخدم بنجاح'}
            
        except Exception as e:
            logger.error(f"خطأ في تحديث المستخدم: {e}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def delete_user(self, user_id):
        """حذف المستخدم"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'المستخدم غير موجود'}
            
            # منع حذف المدير الرئيسي
            if user.username == 'admin':
                return {'success': False, 'error': 'لا يمكن حذف المدير الرئيسي'}
            
            username = user.username
            db.session.delete(user)
            db.session.commit()
            
            logger.info(f"تم حذف المستخدم: {username}")
            
            return {'success': True, 'message': 'تم حذف المستخدم بنجاح'}
            
        except Exception as e:
            logger.error(f"خطأ في حذف المستخدم: {e}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def get_user_permissions(self, user_id):
        """الحصول على صلاحيات المستخدم"""
        user = User.query.get(user_id)
        if not user or not user.role:
            return []
        
        try:
            permissions = json.loads(user.role.permissions or '[]')
            return permissions
        except:
            return []
    
    def has_permission(self, user_id, permission):
        """التحقق من وجود صلاحية للمستخدم"""
        permissions = self.get_user_permissions(user_id)
        return permission in permissions
    
    def get_users_list(self, page=1, per_page=20, search=None):
        """الحصول على قائمة المستخدمين"""
        query = User.query
        
        if search:
            query = query.filter(
                (User.username.contains(search)) |
                (User.full_name.contains(search)) |
                (User.email.contains(search))
            )
        
        users = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return {
            'users': [{
                'id': user.id,
                'username': user.username,
                'full_name': user.full_name,
                'email': user.email,
                'role_name': user.role.display_name if user.role else 'بدون دور',
                'is_active': user.is_active,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat() if user.created_at else None
            } for user in users.items],
            'pagination': {
                'page': users.page,
                'pages': users.pages,
                'per_page': users.per_page,
                'total': users.total,
                'has_next': users.has_next,
                'has_prev': users.has_prev
            }
        }

def require_permission(permission):
    """decorator للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                flash('يجب تسجيل الدخول أولاً', 'error')
                return redirect(url_for('login'))
            
            user_id = session['user_id']
            
            if not user_manager.has_permission(user_id, permission):
                flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
                return redirect(url_for('dashboard'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# إنشاء مثيل مدير المستخدمين
user_manager = UserManager()

# ================================
# Routes لإدارة المستخدمين
# ================================

@user_mgmt_bp.route('/users')
@require_permission(Permission.VIEW_USERS)
def users_list():
    """صفحة قائمة المستخدمين"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    users_data = user_manager.get_users_list(page=page, search=search)
    roles = Role.query.all()
    
    return render_template('admin/users.html', 
                         users_data=users_data, 
                         roles=roles,
                         search=search)

@user_mgmt_bp.route('/users/create', methods=['GET', 'POST'])
@require_permission(Permission.CREATE_USERS)
def create_user():
    """إنشاء مستخدم جديد"""
    if request.method == 'POST':
        data = request.get_json() or request.form
        
        result = user_manager.create_user(
            username=data.get('username'),
            password=data.get('password'),
            email=data.get('email'),
            role_name=data.get('role_name'),
            full_name=data.get('full_name')
        )
        
        if request.is_json:
            return jsonify(result)
        
        if result['success']:
            flash(result['message'], 'success')
        else:
            flash(result['error'], 'error')
        
        return redirect(url_for('user_management.users_list'))
    
    roles = Role.query.all()
    return render_template('admin/create_user.html', roles=roles)

@user_mgmt_bp.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@require_permission(Permission.EDIT_USERS)
def edit_user(user_id):
    """تعديل المستخدم"""
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        data = request.get_json() or request.form
        
        update_data = {}
        for field in ['username', 'email', 'full_name', 'role_name', 'is_active']:
            if field in data:
                update_data[field] = data[field]
        
        if data.get('password'):
            update_data['password'] = data['password']
        
        result = user_manager.update_user(user_id, **update_data)
        
        if request.is_json:
            return jsonify(result)
        
        if result['success']:
            flash(result['message'], 'success')
        else:
            flash(result['error'], 'error')
        
        return redirect(url_for('user_management.users_list'))
    
    roles = Role.query.all()
    return render_template('admin/edit_user.html', user=user, roles=roles)

@user_mgmt_bp.route('/users/<int:user_id>/delete', methods=['POST'])
@require_permission(Permission.DELETE_USERS)
def delete_user(user_id):
    """حذف المستخدم"""
    result = user_manager.delete_user(user_id)
    
    if request.is_json:
        return jsonify(result)
    
    if result['success']:
        flash(result['message'], 'success')
    else:
        flash(result['error'], 'error')
    
    return redirect(url_for('user_management.users_list'))

@user_mgmt_bp.route('/roles')
@require_permission(Permission.MANAGE_ROLES)
def roles_list():
    """صفحة قائمة الأدوار"""
    roles = Role.query.all()
    return render_template('admin/roles.html', roles=roles)

@user_mgmt_bp.route('/roles/create', methods=['POST'])
@require_permission(Permission.MANAGE_ROLES)
def create_role():
    """إنشاء دور جديد"""
    data = request.get_json()
    
    try:
        # التحقق من عدم وجود الدور
        existing_role = Role.query.filter_by(name=data.get('name')).first()
        if existing_role:
            return jsonify({'success': False, 'error': 'اسم الدور موجود بالفعل'})
        
        # إنشاء الدور الجديد
        role = Role(
            name=data.get('name'),
            display_name=data.get('display_name'),
            description=data.get('description'),
            permissions=json.dumps(data.get('permissions', [])),
            is_active=data.get('is_active', True),
            created_at=datetime.utcnow()
        )
        
        db.session.add(role)
        db.session.commit()
        
        logger.info(f"تم إنشاء الدور: {role.display_name}")
        
        return jsonify({'success': True, 'message': 'تم إنشاء الدور بنجاح'})
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الدور: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@user_mgmt_bp.route('/roles/<int:role_id>/edit', methods=['POST'])
@require_permission(Permission.MANAGE_ROLES)
def edit_role(role_id):
    """تعديل الدور"""
    role = Role.query.get_or_404(role_id)
    data = request.get_json()
    
    try:
        # منع تعديل الدور الرئيسي
        if role.name == 'admin':
            return jsonify({'success': False, 'error': 'لا يمكن تعديل الدور الرئيسي'})
        
        # التحقق من عدم تكرار الاسم
        if data.get('name') != role.name:
            existing_role = Role.query.filter_by(name=data.get('name')).first()
            if existing_role:
                return jsonify({'success': False, 'error': 'اسم الدور موجود بالفعل'})
        
        # تحديث البيانات
        role.name = data.get('name', role.name)
        role.display_name = data.get('display_name', role.display_name)
        role.description = data.get('description', role.description)
        role.permissions = json.dumps(data.get('permissions', []))
        role.is_active = data.get('is_active', role.is_active)
        
        db.session.commit()
        
        logger.info(f"تم تحديث الدور: {role.display_name}")
        
        return jsonify({'success': True, 'message': 'تم تحديث الدور بنجاح'})
        
    except Exception as e:
        logger.error(f"خطأ في تحديث الدور: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@user_mgmt_bp.route('/roles/<int:role_id>/delete', methods=['POST'])
@require_permission(Permission.MANAGE_ROLES)
def delete_role(role_id):
    """حذف الدور"""
    role = Role.query.get_or_404(role_id)
    
    try:
        # منع حذف الدور الرئيسي
        if role.name == 'admin':
            return jsonify({'success': False, 'error': 'لا يمكن حذف الدور الرئيسي'})
        
        # التحقق من عدم وجود مستخدمين مرتبطين
        if role.users:
            return jsonify({'success': False, 'error': f'لا يمكن حذف الدور لأنه مرتبط بـ {len(role.users)} مستخدم'})
        
        role_name = role.display_name
        db.session.delete(role)
        db.session.commit()
        
        logger.info(f"تم حذف الدور: {role_name}")
        
        return jsonify({'success': True, 'message': 'تم حذف الدور بنجاح'})
        
    except Exception as e:
        logger.error(f"خطأ في حذف الدور: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

def init_user_management(app):
    """تهيئة نظام إدارة المستخدمين"""
    user_manager.app = app
    app.register_blueprint(user_mgmt_bp)
    
    # إنشاء الأدوار الافتراضية
    with app.app_context():
        user_manager.create_default_roles()
    
    logger.info("تم تهيئة نظام إدارة المستخدمين")
    return user_manager