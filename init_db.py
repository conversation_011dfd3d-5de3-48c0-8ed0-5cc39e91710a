#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تهيئة قاعدة البيانات
Database Initialization Script

يستخدم لإنشاء قاعدة البيانات وإضافة البيانات الافتراضية
"""

import os
import sys
from app import app, db, User, Role, migrate_sqlite_schema

def init_database():
    """تهيئة قاعدة البيانات"""
    print("🔧 جاري تهيئة قاعدة البيانات...")
    
    with app.app_context():
        try:
            # ترحيل مبسط لإضافة role_id إذا لم يوجد
            migrate_sqlite_schema()
            
            # إنشاء الجداول
            db.create_all()
            print("✓ تم إنشاء الجداول بنجاح")
            
            # إنشاء دور المدير
            admin_role = Role.query.filter_by(name="admin").first()
            if not admin_role:
                admin_role = Role(name="admin")
                db.session.add(admin_role)
                db.session.commit()
                print("✓ تم إنشاء دور المدير")
            
            # إنشاء المستخدم الافتراضي
            admin_user = User.query.filter_by(username="admin").first()
            if not admin_user:
                admin_user = User(username="admin", role=admin_role)
                admin_user.set_password("admin123")
                db.session.add(admin_user)
                db.session.commit()
                print("✓ تم إنشاء المستخدم الافتراضي")
                print("   👤 اسم المستخدم: admin")
                print("   🔑 كلمة المرور: admin123")
                print("   ⚠️  يرجى تغيير كلمة المرور فوراً!")
            else:
                print("✓ المستخدم الافتراضي موجود مسبقاً")
            
            print("🎉 تم تهيئة قاعدة البيانات بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            return False

def reset_database():
    """إعادة تعيين قاعدة البيانات (حذف جميع البيانات)"""
    print("⚠️  تحذير: سيتم حذف جميع البيانات!")
    confirm = input("هل تريد المتابعة؟ (yes/no): ").lower().strip()
    
    if confirm in ['yes', 'y', 'نعم']:
        try:
            # حذف ملف قاعدة البيانات
            db_path = os.path.join(os.path.dirname(__file__), 'app.db')
            if os.path.exists(db_path):
                os.remove(db_path)
                print("✓ تم حذف قاعدة البيانات القديمة")
            
            # إعادة تهيئة قاعدة البيانات
            return init_database()
            
        except Exception as e:
            print(f"❌ خطأ في إعادة تعيين قاعدة البيانات: {e}")
            return False
    else:
        print("تم إلغاء العملية")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🗄️  مدير قاعدة البيانات")
    print("Database Manager")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'init':
            init_database()
        elif command == 'reset':
            reset_database()
        elif command == 'help':
            print("الأوامر المتاحة:")
            print("  init  - تهيئة قاعدة البيانات")
            print("  reset - إعادة تعيين قاعدة البيانات")
            print("  help  - عرض هذه المساعدة")
        else:
            print(f"أمر غير معروف: {command}")
            print("استخدم 'help' لعرض الأوامر المتاحة")
    else:
        # تشغيل تفاعلي
        print("اختر العملية:")
        print("1. تهيئة قاعدة البيانات")
        print("2. إعادة تعيين قاعدة البيانات")
        print("3. خروج")
        
        choice = input("اختيارك (1-3): ").strip()
        
        if choice == '1':
            init_database()
        elif choice == '2':
            reset_database()
        elif choice == '3':
            print("تم الخروج")
        else:
            print("اختيار غير صحيح")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()