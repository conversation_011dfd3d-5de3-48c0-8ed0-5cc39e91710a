#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI Configuration for Driver Management System
ملف تكوين WSGI لنظام إدارة السائقين

يستخدم هذا الملف لنشر التطبيق على خوادم الإنتاج مثل:
- Apache with mod_wsgi
- Nginx with uWSGI
- Gunicorn
"""

import os
import sys

# إضافة مسار المشروع إلى Python path
project_path = os.path.dirname(os.path.abspath(__file__))
if project_path not in sys.path:
    sys.path.insert(0, project_path)

# تعيين متغير البيئة للإنتاج
os.environ.setdefault('FLASK_ENV', 'production')

# استيراد التطبيق
from app import app as application

if __name__ == "__main__":
    application.run()