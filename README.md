# 🚗 نظام إدارة السائقين والمركبات
## Driver Management System

نظام شامل لإدارة السائقين والمركبات والمدفوعات والمصروفات مع واجهة عربية سهلة الاستخدام.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)
![SQLite](https://img.shields.io/badge/Database-SQLite-orange.svg)
![Bootstrap](https://img.shields.io/badge/UI-Bootstrap_5-purple.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## 📋 المحتويات

- [الميزات الرئيسية](#-الميزات-الرئيسية)
- [متطلبات النظام](#-متطلبات-النظام)
- [التثبيت والإعداد](#-التثبيت-والإعداد)
- [طرق التشغيل](#-طرق-التشغيل)
- [استخدام النظام](#-استخدام-النظام)
- [هيكل المشروع](#-هيكل-المشروع)
- [API Documentation](#-api-documentation)
- [النشر](#-النشر)
- [الاختبارات](#-الاختبارات)
- [المساهمة](#-المساهمة)
- [الترخيص](#-الترخيص)

## ✨ الميزات الرئيسية

### 👥 إدارة السائقين
- ✅ إضافة وتعديل وحذف السائقين
- ✅ تتبع أرصدة السائقين
- ✅ البحث والفلترة المتقدمة
- ✅ إحصائيات مفصلة لكل سائق

### 🚛 إدارة المركبات
- ✅ ربط المركبات بالسائقين
- ✅ تتبع معلومات المركبات (رقم اللوحة، النوع، الموديل)
- ✅ إدارة صيانة المركبات
- ✅ تقارير المركبات

### 💰 إدارة المدفوعات
- ✅ تسجيل المدفوعات الداخلة والخارجة
- ✅ طرق دفع متعددة (نقد، تحويل، شيك)
- ✅ تتبع تاريخ المدفوعات
- ✅ تحديث الأرصدة تلقائياً

### 📊 المصروفات والرواتب
- ✅ تسجيل مصروفات المركبات
- ✅ إدارة رواتب السائقين
- ✅ تصنيف المصروفات
- ✅ تقارير مالية شاملة

### 📈 التقارير والإحصائيات
- ✅ تقارير مالية مفصلة
- ✅ إحصائيات الأداء
- ✅ رسوم بيانية تفاعلية
- ✅ تصدير البيانات (Excel, PDF)

### 🔧 ميزات إضافية
- ✅ واجهة عربية كاملة
- ✅ تصميم متجاوب (Mobile-Friendly)
- ✅ نظام نسخ احتياطية
- ✅ فحص صحة النظام
- ✅ طباعة التقارير
- ✅ إشعارات ذكية

## 🔧 متطلبات النظام

### المتطلبات الأساسية
- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- 512 MB RAM كحد أدنى
- 100 MB مساحة تخزين

### المتطلبات الاختيارية
- Docker (للنشر بالحاويات)
- Nginx (للنشر في الإنتاج)
- SSL Certificate (للأمان)

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/driver-management-system.git
cd driver-management-system
```

### 2. إنشاء بيئة افتراضية
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python run.py --setup
```

### 5. إنشاء بيانات تجريبية (اختياري)
```bash
python init_db.py seed
```

## 🎯 طرق التشغيل

### التشغيل السريع
```bash
python app.py
```

### التشغيل المتقدم
```bash
# بيئة التطوير
python run.py --dev

# بيئة الإنتاج
python run.py --prod

# بيئة الاختبار
python run.py --test

# فحص المتطلبات
python run.py --check

# عرض معلومات النظام
python run.py --info
```

### التشغيل بـ Docker
```bash
# بناء الصورة
docker build -t driver-management .

# تشغيل الحاوية
docker run -p 5000:5000 driver-management

# أو استخدام docker-compose
docker-compose up -d
```

## 📱 استخدام النظام

### تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### الصفحات الرئيسية
- **لوحة التحكم**: `/dashboard` - عرض السائقين والإحصائيات
- **المدفوعات**: `/payments` - إدارة المدفوعات
- **المصروفات**: `/expenses` - تسجيل المصروفات
- **التقارير**: `/reports` - التقارير والإحصائيات
- **الإعدادات**: `/settings` - إعدادات النظام

### العمليات الأساسية

#### إضافة سائق جديد
1. اذهب إلى لوحة التحكم
2. اضغط على "إضافة سائق"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

#### تسجيل مدفوعة
1. اذهب إلى صفحة المدفوعات
2. اضغط على "إضافة مدفوعة"
3. اختر السائق والمبلغ ونوع المدفوعة
4. اضغط "حفظ"

#### إنشاء تقرير
1. اذهب إلى صفحة التقارير
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية (إن أردت)
4. اضغط على "تصدير" أو "طباعة"

## 📁 هيكل المشروع

```
driver-management-system/
├── 📄 app.py                 # التطبيق الرئيسي
├── 📄 run.py                 # ملف التشغيل المتقدم
├── 📄 wsgi.py                # إعداد WSGI للإنتاج
├── 📄 init_db.py             # إعداد قاعدة البيانات
├── 📄 tests.py               # الاختبارات
├── 📄 requirements.txt       # متطلبات Python
├── 📄 Dockerfile             # إعداد Docker
├── 📄 docker-compose.yml     # إعداد Docker Compose
├── 📄 nginx.conf             # إعداد Nginx
├── 📄 README.md              # هذا الملف
├── 📁 templates/             # قوالب HTML
│   ├── 📄 base.html
│   ├── 📄 dashboard.html
│   ├── 📄 login.html
│   ├── 📄 payments.html
│   ├── 📄 reports.html
│   ├── 📄 settings.html
│   └── 📄 print_report.html
├── 📁 static/                # الملفات الثابتة
│   ├── 📄 styles.css
│   ├── 📄 app.js
│   └── 📁 images/
├── 📁 exports/               # ملفات التصدير
├── 📁 backups/               # النسخ الاحتياطية
└── 📄 drivers.db             # قاعدة البيانات
```

## 🔌 API Documentation

### الإحصائيات
```http
GET /api/stats
```
إرجاع إحصائيات النظام الحالية

**الاستجابة:**
```json
{
  "drivers_count": 25,
  "positive_balance_count": 15,
  "negative_balance_count": 8,
  "total_balance": 12500.50,
  "vehicles_count": 30,
  "payments_count": 150
}
```

### فحص الصحة
```http
GET /health
```
فحص صحة النظام وقاعدة البيانات

**الاستجابة:**
```json
{
  "status": "healthy",
  "database": "connected",
  "timestamp": "2024-01-15T10:30:00"
}
```

### النسخ الاحتياطية
```http
GET /backup
```
إنشاء نسخة احتياطية من قاعدة البيانات

```http
POST /restore
```
استعادة نسخة احتياطية

## 🌐 النشر

### النشر على خادم محلي
```bash
# استخدام Gunicorn
gunicorn --bind 0.0.0.0:8000 --workers 4 wsgi:application

# استخدام uWSGI
uwsgi --http :8000 --module wsgi:application
```

### النشر بـ Docker
```bash
# بناء ونشر
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

### النشر على السحابة
- **Heroku**: استخدم `Procfile` المرفق
- **AWS**: استخدم Elastic Beanstalk
- **DigitalOcean**: استخدم App Platform
- **Google Cloud**: استخدم App Engine

### إعداد HTTPS
1. احصل على شهادة SSL
2. ضع الشهادات في مجلد `ssl/`
3. فعل HTTPS في `nginx.conf`
4. أعد تشغيل Nginx

## 🧪 الاختبارات

### تشغيل جميع الاختبارات
```bash
python tests.py
```

### تشغيل اختبارات محددة
```bash
# اختبارات النماذج
python -m unittest tests.ModelTests

# اختبارات المسارات
python -m unittest tests.RouteTests

# اختبارات API
python -m unittest tests.APITests
```

### تقرير التغطية
```bash
pip install coverage
coverage run tests.py
coverage report
coverage html
```

## 🔒 الأمان

### الميزات الأمنية المطبقة
- ✅ حماية من SQL Injection
- ✅ حماية من XSS
- ✅ حماية من CSRF
- ✅ تشفير كلمات المرور
- ✅ جلسات آمنة
- ✅ رؤوس أمان HTTP

### أفضل الممارسات
- استخدم HTTPS في الإنتاج
- غير كلمة مرور المدير الافتراضية
- فعل جدار الحماية
- راقب السجلات بانتظام
- حدث النظام دورياً

## 🔧 الصيانة

### النسخ الاحتياطية
```bash
# إنشاء نسخة احتياطية يدوية
python -c "from app import create_backup; create_backup()"

# جدولة النسخ الاحتياطية (Linux)
crontab -e
# إضافة: 0 2 * * * /path/to/backup_script.sh
```

### مراقبة الأداء
- راقب استخدام الذاكرة
- راقب مساحة القرص الصلب
- راقب أوقات الاستجابة
- راقب أخطاء النظام

### التحديثات
```bash
# تحديث المتطلبات
pip install --upgrade -r requirements.txt

# تحديث قاعدة البيانات
python init_db.py migrate
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. **Fork** المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. تنفيذ التغييرات (`git commit -m 'Add amazing feature'`)
4. رفع التغييرات (`git push origin feature/amazing-feature`)
5. إنشاء **Pull Request**

### إرشادات المساهمة
- اتبع معايير الكود المستخدمة
- أضف اختبارات للميزات الجديدة
- حدث الوثائق عند الحاجة
- تأكد من نجاح جميع الاختبارات

## 📞 الدعم الفني

### طرق التواصل
- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [إنشاء مشكلة جديدة](https://github.com/your-username/driver-management-system/issues)
- **الوثائق**: [Wiki](https://github.com/your-username/driver-management-system/wiki)

### الأسئلة الشائعة

**س: كيف أغير كلمة مرور المدير؟**
ج: اذهب إلى الإعدادات > تغيير كلمة المرور

**س: كيف أستعيد البيانات المحذوفة؟**
ج: استخدم النسخة الاحتياطية من الإعدادات > النسخ الاحتياطية

**س: هل يمكن استخدام قاعدة بيانات أخرى غير SQLite؟**
ج: نعم، يمكن تكوين PostgreSQL أو MySQL في `app.py`

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

```
MIT License

Copyright (c) 2024 Driver Management System

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🎉 شكر خاص

- **Bootstrap** - للواجهة الجميلة
- **Flask** - لإطار العمل الرائع
- **Font Awesome** - للأيقونات
- **Chart.js** - للرسوم البيانية
- **OpenPyXL** - لتصدير Excel

---

<div align="center">

**صنع بـ ❤️ في المملكة العربية السعودية**

[⬆ العودة للأعلى](#-نظام-إدارة-السائقين-والمركبات)

</div>