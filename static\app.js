// ملف JavaScript الرئيسي لنظام إدارة السائقين

// تحديث الإحصائيات تلقائياً
function updateStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // تحديث عدد السائقين
            const driversCount = document.getElementById('drivers-count');
            if (driversCount) {
                driversCount.textContent = data.drivers_count;
            }
            
            // تحديث الأرصدة
            const totalBalance = document.getElementById('total-balance');
            if (totalBalance) {
                totalBalance.textContent = data.total_balance.toFixed(2);
            }
        })
        .catch(error => console.error('خطأ في تحديث الإحصائيات:', error));
}

// تأكيد الحذف
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من الحذف؟');
}

// تنسيق الأرقام
function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(num);
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// إظهار رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // إخفاء التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

// التحقق من صحة النماذج
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

// تحديث الرصيد عند إضافة مدفوعة
function updateBalancePreview() {
    const driverSelect = document.getElementById('driver_id');
    const amountInput = document.getElementById('amount');
    const directionSelect = document.getElementById('direction');
    const balancePreview = document.getElementById('balance-preview');
    
    if (!driverSelect || !amountInput || !directionSelect || !balancePreview) {
        return;
    }
    
    const driverId = driverSelect.value;
    const amount = parseFloat(amountInput.value) || 0;
    const direction = directionSelect.value;
    
    if (driverId && amount > 0) {
        // الحصول على الرصيد الحالي من البيانات المخزنة
        const currentBalance = parseFloat(driverSelect.selectedOptions[0].dataset.balance) || 0;
        let newBalance = currentBalance;
        
        if (direction === 'in') {
            newBalance += amount;
        } else if (direction === 'out') {
            newBalance -= amount;
        }
        
        balancePreview.innerHTML = `
            <div class="alert alert-info">
                <strong>الرصيد الحالي:</strong> ${formatNumber(currentBalance)}<br>
                <strong>الرصيد بعد العملية:</strong> 
                <span class="${newBalance >= 0 ? 'text-success' : 'text-danger'}">
                    ${formatNumber(newBalance)}
                </span>
            </div>
        `;
    } else {
        balancePreview.innerHTML = '';
    }
}

// البحث المباشر
function setupLiveSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (!searchInput) return;
    
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 2 || this.value.length === 0) {
                this.form.submit();
            }
        }, 500);
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تهيئة البحث المباشر
    setupLiveSearch();
    
    // تحديث معاينة الرصيد في نماذج المدفوعات
    const paymentForm = document.querySelector('form[action*="payment"]');
    if (paymentForm) {
        ['driver_id', 'amount', 'direction'].forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (field) {
                field.addEventListener('change', updateBalancePreview);
                field.addEventListener('input', updateBalancePreview);
            }
        });
    }
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
});

// دوال مساعدة للرسوم البيانية
function createChart(canvasId, type, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;
    
    return new Chart(ctx, {
        type: type,
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            ...options
        }
    });
}

// تصدير البيانات
function exportData(type, format = 'excel') {
    const url = `/export/${type}?format=${format}`;
    window.open(url, '_blank');
}

// طباعة التقرير
function printReport(type) {
    const url = `/print-report?type=${type}`;
    window.open(url, '_blank');
}