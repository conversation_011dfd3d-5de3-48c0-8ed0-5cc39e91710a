<!-- templates/expenses.html -->
<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>إدارة المصروفات</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
  <div class="container">
    <a class="navbar-brand" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
    <div class="d-flex">
      <div class="dropdown me-2">
        <button class="btn btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
          إدارة
        </button>
        <ul class="dropdown-menu">
          <li><a class="dropdown-item" href="{{ url_for('dashboard') }}">السائقون</a></li>
          <li><a class="dropdown-item" href="{{ url_for('vehicles') }}">المركبات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('payments') }}">المدفوعات</a></li>
          <li><a class="dropdown-item" href="{{ url_for('salaries') }}">الرواتب</a></li>
          <li><a class="dropdown-item" href="{{ url_for('expenses') }}">المصروفات</a></li>
        </ul>
      </div>
      <a href="{{ url_for('reports') }}" class="btn btn-outline-light me-2">التقارير</a>
      <a href="{{ url_for('settings') }}" class="btn btn-outline-light me-2">الإعدادات</a>
      <a href="{{ url_for('logout') }}" class="btn btn-outline-light">خروج</a>
    </div>
  </div>
</nav>

<div class="container">
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-3">
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} mb-2" role="alert">{{ message }}</div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <div class="d-flex justify-content-between align-items-center mb-3">
    <h4>إدارة المصروفات</h4>
    <a href="{{ url_for('add_expense') }}" class="btn btn-primary">إضافة مصروف</a>
  </div>

  <div class="card shadow-sm">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped align-middle">
          <thead>
            <tr>
              <th>#</th>
              <th>السائق</th>
              <th>المركبة</th>
              <th>المبلغ</th>
              <th>الفئة</th>
              <th>التاريخ</th>
              <th>ملاحظة</th>
              <th class="text-center">إجراءات</th>
            </tr>
          </thead>
          <tbody>
            {% for e in expenses %}
              <tr>
                <td>{{ e.id }}</td>
                <td>
                  {% if e.driver %}
                    <a href="{{ url_for('driver_detail', driver_id=e.driver.id) }}">{{ e.driver.name }}</a>
                  {% else %}
                    <span class="text-muted">غير محدد</span>
                  {% endif %}
                </td>
                <td>
                  {% if e.vehicle %}
                    {{ e.vehicle.plate_number }}
                  {% else %}
                    <span class="text-muted">غير محدد</span>
                  {% endif %}
                </td>
                <td>{{ "%.2f"|format(e.amount) }}</td>
                <td>{{ e.category or '-' }}</td>
                <td>{{ e.occurred_at.strftime('%Y-%m-%d %H:%M') }}</td>
                <td>{{ e.note or '-' }}</td>
                <td class="text-center">
                  <form action="{{ url_for('delete_expense', expense_id=e.id) }}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('تأكيد حذف المصروف؟');">حذف</button>
                  </form>
                </td>
              </tr>
            {% else %}
              <tr>
                <td colspan="8" class="text-center text-muted">لا توجد مصروفات حتى الآن.</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>