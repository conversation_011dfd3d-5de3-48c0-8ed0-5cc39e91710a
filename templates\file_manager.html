{% extends "base.html" %}

{% block title %}إدارة الملفات{% endblock %}

{% block extra_css %}
<style>
.file-item {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.file-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.file-thumbnail {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background-color: #f8f9fa;
}

.file-icon {
    font-size: 4rem;
    color: #6c757d;
}

.file-category-images { border-left: 4px solid #28a745; }
.file-category-documents { border-left: 4px solid #007bff; }
.file-category-archives { border-left: 4px solid #ffc107; }
.file-category-videos { border-left: 4px solid #dc3545; }
.file-category-audio { border-left: 4px solid #6f42c1; }
.file-category-other { border-left: 4px solid #6c757d; }

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}

.progress-container {
    display: none;
    margin-top: 15px;
}

.file-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.storage-bar {
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- عنوان الصفحة -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-folder-open"></i> إدارة الملفات</h2>
        <div>
          <button onclick="showUploadModal()" class="btn btn-primary">
            <i class="fas fa-upload"></i> رفع ملفات
          </button>
          <button onclick="createArchive()" class="btn btn-outline-info">
            <i class="fas fa-archive"></i> إنشاء أرشيف
          </button>
          <button onclick="cleanupFiles()" class="btn btn-outline-warning">
            <i class="fas fa-broom"></i> تنظيف
          </button>
          <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- إحصائيات التخزين -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-chart-pie"></i> إحصائيات التخزين</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <div class="text-center">
                <h4 class="text-primary" id="totalFiles">0</h4>
                <small class="text-muted">إجمالي الملفات</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="text-center">
                <h4 class="text-success" id="totalSize">0 MB</h4>
                <small class="text-muted">المساحة المستخدمة</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="text-center">
                <h4 class="text-info" id="availableSpace">0 GB</h4>
                <small class="text-muted">المساحة المتاحة</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="text-center">
                <h4 class="text-warning" id="selectedCount">0</h4>
                <small class="text-muted">ملفات محددة</small>
              </div>
            </div>
          </div>
          
          <!-- شريط المساحة -->
          <div class="mt-3">
            <small class="text-muted">استخدام المساحة:</small>
            <div class="progress storage-bar mt-1">
              <div class="progress-bar" id="storageBar" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- فلاتر وبحث -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <select class="form-select" id="categoryFilter" onchange="filterFiles()">
                <option value="">جميع الفئات</option>
                <option value="images">الصور</option>
                <option value="documents">المستندات</option>
                <option value="archives">الأرشيف</option>
                <option value="videos">الفيديو</option>
                <option value="audio">الصوت</option>
                <option value="other">أخرى</option>
              </select>
            </div>
            <div class="col-md-4">
              <input type="text" class="form-control" id="searchInput" 
                     placeholder="البحث في الملفات..." onkeyup="searchFiles()">
            </div>
            <div class="col-md-4">
              <div class="btn-group w-100">
                <button class="btn btn-outline-secondary" onclick="selectAll()">
                  <i class="fas fa-check-square"></i> تحديد الكل
                </button>
                <button class="btn btn-outline-secondary" onclick="deselectAll()">
                  <i class="fas fa-square"></i> إلغاء التحديد
                </button>
                <button class="btn btn-outline-danger" onclick="deleteSelected()">
                  <i class="fas fa-trash"></i> حذف المحدد
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- قائمة الملفات -->
  <div class="row" id="filesList">
    <!-- سيتم تحميل الملفات هنا -->
  </div>

  <!-- Pagination -->
  <div class="row mt-4">
    <div class="col-12">
      <nav id="paginationNav" style="display: none;">
        <ul class="pagination justify-content-center" id="paginationList">
          <!-- سيتم إنشاء الصفحات هنا -->
        </ul>
      </nav>
    </div>
  </div>
</div>

<!-- Modal رفع الملفات -->
<div class="modal fade" id="uploadModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">رفع ملفات جديدة</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
          <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
          <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
          <p class="text-muted">الحد الأقصى: 16MB لكل ملف</p>
          <input type="file" id="fileInput" multiple style="display: none;" 
                 accept=".png,.jpg,.jpeg,.gif,.pdf,.doc,.docx,.xls,.xlsx,.zip,.mp4,.mp3">
        </div>
        
        <div class="mt-3">
          <label for="fileDescription" class="form-label">وصف الملفات (اختياري)</label>
          <textarea class="form-control" id="fileDescription" rows="2" 
                    placeholder="اكتب وصفاً للملفات المرفوعة"></textarea>
        </div>
        
        <!-- قائمة الملفات المحددة -->
        <div id="selectedFilesList" class="mt-3" style="display: none;">
          <h6>الملفات المحددة:</h6>
          <div id="selectedFiles"></div>
        </div>
        
        <!-- شريط التقدم -->
        <div class="progress-container">
          <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 id="uploadProgress" style="width: 0%"></div>
          </div>
          <small id="uploadStatus" class="text-muted">جاري الرفع...</small>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-primary" id="uploadBtn" onclick="uploadFiles()">
          <i class="fas fa-upload"></i> رفع الملفات
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal معاينة الملف -->
<div class="modal fade" id="previewModal" tabindex="-1">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="previewTitle">معاينة الملف</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body text-center" id="previewContent">
        <!-- سيتم تحميل المحتوى هنا -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
        <a href="#" class="btn btn-primary" id="downloadBtn" target="_blank">
          <i class="fas fa-download"></i> تحميل
        </a>
      </div>
    </div>
  </div>
</div>

<script>
let currentPage = 1;
let currentCategory = '';
let currentSearch = '';
let selectedFiles = new Set();
let allFiles = [];

// تحميل الملفات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  loadFiles();
  loadStorageStats();
  setupDragAndDrop();
});

// تحميل قائمة الملفات
function loadFiles(page = 1) {
  currentPage = page;
  
  const params = new URLSearchParams({
    page: page,
    category: currentCategory,
    search: currentSearch
  });
  
  fetch(`/api/files/list?${params}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        displayFiles(data.files);
        updatePagination(data.pagination);
        allFiles = data.files;
      } else {
        console.error('خطأ في تحميل الملفات:', data.error);
      }
    })
    .catch(error => {
      console.error('خطأ:', error);
    });
}

// عرض الملفات
function displayFiles(files) {
  const container = document.getElementById('filesList');
  
  if (files.length === 0) {
    container.innerHTML = `
      <div class="col-12">
        <div class="text-center py-5">
          <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">لا توجد ملفات</h5>
          <p class="text-muted">قم برفع أول ملف للبدء</p>
          <button onclick="showUploadModal()" class="btn btn-primary">
            <i class="fas fa-upload"></i> رفع ملفات
          </button>
        </div>
      </div>
    `;
    return;
  }
  
  let html = '';
  files.forEach(file => {
    const isSelected = selectedFiles.has(file.filename);
    const categoryClass = `file-category-${file.category}`;
    
    html += `
      <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
        <div class="file-item ${categoryClass} ${isSelected ? 'border-primary' : ''}">
          <div class="position-relative">
            ${getFilePreview(file)}
            <div class="file-actions position-absolute top-0 end-0 p-2">
              <div class="btn-group-vertical">
                <button class="btn btn-sm btn-outline-light" onclick="toggleFileSelection('${file.filename}')" 
                        title="${isSelected ? 'إلغاء التحديد' : 'تحديد'}">
                  <i class="fas fa-${isSelected ? 'check-square' : 'square'}"></i>
                </button>
                <button class="btn btn-sm btn-outline-light" onclick="previewFile('${file.filename}')" title="معاينة">
                  <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-light" onclick="downloadFile('${file.filename}')" title="تحميل">
                  <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteFile('${file.filename}')" title="حذف">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="p-3">
            <h6 class="mb-1 text-truncate" title="${file.original_name}">${file.original_name}</h6>
            <small class="text-muted d-block">${formatFileSize(file.size)}</small>
            <small class="text-muted d-block">${formatDate(file.uploaded_at)}</small>
            ${file.description ? `<small class="text-info d-block mt-1">${file.description}</small>` : ''}
          </div>
        </div>
      </div>
    `;
  });
  
  container.innerHTML = html;
  updateSelectedCount();
}

// الحصول على معاينة الملف
function getFilePreview(file) {
  if (file.category === 'images' && file.thumbnail) {
    return `<img src="${file.thumbnail}" class="file-thumbnail" alt="${file.original_name}">`;
  }
  
  const icons = {
    'images': 'fa-image',
    'documents': 'fa-file-alt',
    'archives': 'fa-file-archive',
    'videos': 'fa-video',
    'audio': 'fa-music',
    'other': 'fa-file'
  };
  
  const icon = icons[file.category] || 'fa-file';
  
  return `
    <div class="file-thumbnail d-flex align-items-center justify-content-center">
      <i class="fas ${icon} file-icon"></i>
    </div>
  `;
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// تنسيق التاريخ
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
}

// تحديد/إلغاء تحديد ملف
function toggleFileSelection(filename) {
  if (selectedFiles.has(filename)) {
    selectedFiles.delete(filename);
  } else {
    selectedFiles.add(filename);
  }
  
  loadFiles(currentPage); // إعادة تحميل لتحديث العرض
}

// تحديد جميع الملفات
function selectAll() {
  allFiles.forEach(file => selectedFiles.add(file.filename));
  loadFiles(currentPage);
}

// إلغاء تحديد جميع الملفات
function deselectAll() {
  selectedFiles.clear();
  loadFiles(currentPage);
}

// تحديث عداد الملفات المحددة
function updateSelectedCount() {
  document.getElementById('selectedCount').textContent = selectedFiles.size;
}

// فلترة الملفات
function filterFiles() {
  currentCategory = document.getElementById('categoryFilter').value;
  currentPage = 1;
  loadFiles();
}

// البحث في الملفات
function searchFiles() {
  currentSearch = document.getElementById('searchInput').value;
  currentPage = 1;
  loadFiles();
}

// تحميل إحصائيات التخزين
function loadStorageStats() {
  fetch('/api/files/stats')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const stats = data.stats;
        document.getElementById('totalFiles').textContent = stats.total_files;
        document.getElementById('totalSize').textContent = formatFileSize(stats.total_size);
        document.getElementById('availableSpace').textContent = formatFileSize(stats.available_space);
        
        // شريط المساحة
        const usedPercentage = (stats.total_size / (stats.total_size + stats.available_space)) * 100;
        document.getElementById('storageBar').style.width = usedPercentage + '%';
      }
    })
    .catch(error => {
      console.error('خطأ في تحميل الإحصائيات:', error);
    });
}

// إعداد السحب والإفلات
function setupDragAndDrop() {
  const uploadArea = document.getElementById('uploadArea');
  
  ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, preventDefaults, false);
  });
  
  function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }
  
  ['dragenter', 'dragover'].forEach(eventName => {
    uploadArea.addEventListener(eventName, highlight, false);
  });
  
  ['dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, unhighlight, false);
  });
  
  function highlight(e) {
    uploadArea.classList.add('dragover');
  }
  
  function unhighlight(e) {
    uploadArea.classList.remove('dragover');
  }
  
  uploadArea.addEventListener('drop', handleDrop, false);
  
  function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    handleFiles(files);
  }
}

// معالجة الملفات المحددة
function handleFiles(files) {
  const fileInput = document.getElementById('fileInput');
  fileInput.files = files;
  displaySelectedFiles(files);
}

// عرض الملفات المحددة
function displaySelectedFiles(files) {
  const container = document.getElementById('selectedFiles');
  const listContainer = document.getElementById('selectedFilesList');
  
  if (files.length === 0) {
    listContainer.style.display = 'none';
    return;
  }
  
  let html = '';
  Array.from(files).forEach((file, index) => {
    html += `
      <div class="d-flex justify-content-between align-items-center border rounded p-2 mb-2">
        <div>
          <strong>${file.name}</strong>
          <small class="text-muted d-block">${formatFileSize(file.size)}</small>
        </div>
        <button class="btn btn-sm btn-outline-danger" onclick="removeSelectedFile(${index})">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;
  });
  
  container.innerHTML = html;
  listContainer.style.display = 'block';
}

// إزالة ملف من القائمة المحددة
function removeSelectedFile(index) {
  const fileInput = document.getElementById('fileInput');
  const dt = new DataTransfer();
  
  Array.from(fileInput.files).forEach((file, i) => {
    if (i !== index) {
      dt.items.add(file);
    }
  });
  
  fileInput.files = dt.files;
  displaySelectedFiles(fileInput.files);
}

// عرض نافذة الرفع
function showUploadModal() {
  const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
  modal.show();
}

// رفع الملفات
function uploadFiles() {
  const fileInput = document.getElementById('fileInput');
  const description = document.getElementById('fileDescription').value;
  
  if (fileInput.files.length === 0) {
    alert('يرجى اختيار ملفات للرفع');
    return;
  }
  
  const formData = new FormData();
  Array.from(fileInput.files).forEach(file => {
    formData.append('files', file);
  });
  
  if (description) {
    formData.append('description', description);
  }
  
  // إظهار شريط التقدم
  document.querySelector('.progress-container').style.display = 'block';
  document.getElementById('uploadBtn').disabled = true;
  
  fetch('/api/files/upload', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert('تم رفع الملفات بنجاح');
      bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
      loadFiles();
      loadStorageStats();
      
      // إعادة تعيين النموذج
      fileInput.value = '';
      document.getElementById('fileDescription').value = '';
      document.getElementById('selectedFilesList').style.display = 'none';
    } else {
      alert('خطأ في رفع الملفات: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في رفع الملفات');
  })
  .finally(() => {
    document.querySelector('.progress-container').style.display = 'none';
    document.getElementById('uploadBtn').disabled = false;
  });
}

// معاينة الملف
function previewFile(filename) {
  fetch(`/api/files/info/${filename}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const file = data.file;
        const modal = new bootstrap.Modal(document.getElementById('previewModal'));
        
        document.getElementById('previewTitle').textContent = file.original_name;
        document.getElementById('downloadBtn').href = file.url;
        
        let content = '';
        if (file.category === 'images') {
          content = `<img src="${file.url}" class="img-fluid" alt="${file.original_name}">`;
        } else if (file.category === 'videos') {
          content = `<video controls class="w-100" style="max-height: 500px;">
                      <source src="${file.url}" type="${file.mime_type}">
                      متصفحك لا يدعم تشغيل الفيديو.
                    </video>`;
        } else if (file.category === 'audio') {
          content = `<audio controls class="w-100">
                      <source src="${file.url}" type="${file.mime_type}">
                      متصفحك لا يدعم تشغيل الصوت.
                    </audio>`;
        } else {
          content = `
            <div class="text-center py-5">
              <i class="fas fa-file fa-5x text-muted mb-3"></i>
              <h5>${file.original_name}</h5>
              <p class="text-muted">الحجم: ${formatFileSize(file.size)}</p>
              <p class="text-muted">النوع: ${file.mime_type || 'غير محدد'}</p>
              ${file.description ? `<p class="text-info">${file.description}</p>` : ''}
            </div>
          `;
        }
        
        document.getElementById('previewContent').innerHTML = content;
        modal.show();
      }
    })
    .catch(error => {
      console.error('خطأ:', error);
      alert('حدث خطأ في معاينة الملف');
    });
}

// تحميل الملف
function downloadFile(filename) {
  fetch(`/api/files/info/${filename}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const link = document.createElement('a');
        link.href = data.file.url;
        link.download = data.file.original_name;
        link.click();
      }
    })
    .catch(error => {
      console.error('خطأ:', error);
      alert('حدث خطأ في تحميل الملف');
    });
}

// حذف ملف
function deleteFile(filename) {
  if (!confirm('هل أنت متأكد من حذف هذا الملف؟\n\nلا يمكن التراجع عن هذا الإجراء!')) {
    return;
  }
  
  fetch(`/api/files/delete/${filename}`, {
    method: 'DELETE'
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert('تم حذف الملف بنجاح');
      selectedFiles.delete(filename);
      loadFiles();
      loadStorageStats();
    } else {
      alert('خطأ في حذف الملف: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في حذف الملف');
  });
}

// حذف الملفات المحددة
function deleteSelected() {
  if (selectedFiles.size === 0) {
    alert('يرجى تحديد ملفات للحذف');
    return;
  }
  
  if (!confirm(`هل أنت متأكد من حذف ${selectedFiles.size} ملف؟\n\nلا يمكن التراجع عن هذا الإجراء!`)) {
    return;
  }
  
  const filenames = Array.from(selectedFiles);
  
  fetch('/api/files/delete-multiple', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ filenames: filenames })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(`تم حذف ${data.deleted_count} ملف بنجاح`);
      selectedFiles.clear();
      loadFiles();
      loadStorageStats();
    } else {
      alert('خطأ في حذف الملفات: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في حذف الملفات');
  });
}

// إنشاء أرشيف
function createArchive() {
  if (selectedFiles.size === 0) {
    alert('يرجى تحديد ملفات لإنشاء الأرشيف');
    return;
  }
  
  const archiveName = prompt('اسم الأرشيف:', `archive_${new Date().toISOString().slice(0,10)}.zip`);
  if (!archiveName) return;
  
  const filenames = Array.from(selectedFiles);
  
  fetch('/api/files/create-archive', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ 
      filenames: filenames,
      archive_name: archiveName
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert('تم إنشاء الأرشيف بنجاح');
      loadFiles();
      loadStorageStats();
    } else {
      alert('خطأ في إنشاء الأرشيف: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في إنشاء الأرشيف');
  });
}

// تنظيف الملفات القديمة
function cleanupFiles() {
  const days = prompt('حذف الملفات الأقدم من كم يوم؟', '30');
  if (!days || isNaN(days)) return;
  
  if (!confirm(`هل أنت متأكد من حذف الملفات الأقدم من ${days} يوم؟`)) {
    return;
  }
  
  fetch('/api/files/cleanup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ days: parseInt(days) })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(`تم حذف ${data.deleted_count} ملف وتوفير ${formatFileSize(data.freed_space)}`);
      loadFiles();
      loadStorageStats();
    } else {
      alert('خطأ في تنظيف الملفات: ' + data.error);
    }
  })
  .catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في تنظيف الملفات');
  });
}

// تحديث الصفحات
function updatePagination(pagination) {
  const nav = document.getElementById('paginationNav');
  const list = document.getElementById('paginationList');
  
  if (pagination.pages <= 1) {
    nav.style.display = 'none';
    return;
  }
  
  nav.style.display = 'block';
  
  let html = '';
  
  // السابق
  if (pagination.has_prev) {
    html += `<li class="page-item">
              <a class="page-link" href="#" onclick="loadFiles(${pagination.page - 1})">السابق</a>
            </li>`;
  }
  
  // الصفحات
  for (let i = 1; i <= pagination.pages; i++) {
    if (i === pagination.page) {
      html += `<li class="page-item active">
                <span class="page-link">${i}</span>
              </li>`;
    } else {
      html += `<li class="page-item">
                <a class="page-link" href="#" onclick="loadFiles(${i})">${i}</a>
              </li>`;
    }
  }
  
  // التالي
  if (pagination.has_next) {
    html += `<li class="page-item">
              <a class="page-link" href="#" onclick="loadFiles(${pagination.page + 1})">التالي</a>
            </li>`;
  }
  
  list.innerHTML = html;
}

// معالج اختيار الملفات
document.getElementById('fileInput').addEventListener('change', function(e) {
  displaySelectedFiles(e.target.files);
});
</script>
{% endblock %}