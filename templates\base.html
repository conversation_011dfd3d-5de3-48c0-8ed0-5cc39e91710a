<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة السائقين والمركبات{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Moment.js for date formatting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }
        
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .btn {
            border-radius: 0.375rem;
        }
        
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        
        .notification-badge {
            font-size: 0.7rem;
            min-width: 1.2rem;
            height: 1.2rem;
            line-height: 1.2rem;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #fff;
            border-left: 1px solid #dee2e6;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #e9ecef;
            color: #007bff;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .text-primary { color: #007bff !important; }
        .text-success { color: #28a745 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .text-info { color: #17a2b8 !important; }
        
        .bg-primary { background-color: #007bff !important; }
        .bg-success { background-color: #28a745 !important; }
        .bg-warning { background-color: #ffc107 !important; }
        .bg-danger { background-color: #dc3545 !important; }
        .bg-info { background-color: #17a2b8 !important; }
        
        /* تحسينات للطباعة */
        @media print {
            .no-print {
                display: none !important;
            }
            
            .card {
                box-shadow: none;
                border: 1px solid #dee2e6;
            }
            
            body {
                background-color: white;
            }
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 576px) {
            .table-responsive {
                font-size: 0.875rem;
            }
            
            .btn-group .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-car"></i> نظام إدارة السائقين
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('payments') }}">
                            <i class="fas fa-money-bill-wave"></i> المدفوعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('expenses') }}">
                            <i class="fas fa-receipt"></i> المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('monitoring_dashboard') }}">
                            <i class="fas fa-chart-line"></i> المراقبة
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="{{ url_for('notifications_page') }}">
                            <i class="fas fa-bell"></i> الإشعارات
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge" style="display: none;">
                                0
                            </span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('backup_page') }}">
                            <i class="fas fa-database"></i> النسخ الاحتياطية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('file_manager_page') }}">
                            <i class="fas fa-folder-open"></i> إدارة الملفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- عرض الرسائل -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container-fluid mb-4">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {% if category == 'success' %}
                                <i class="fas fa-check-circle"></i>
                            {% elif category == 'error' or category == 'danger' %}
                                <i class="fas fa-exclamation-circle"></i>
                            {% elif category == 'warning' %}
                                <i class="fas fa-exclamation-triangle"></i>
                            {% else %}
                                <i class="fas fa-info-circle"></i>
                            {% endif %}
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- محتوى الصفحة -->
        {% block content %}{% endblock %}
    </main>

    <!-- تذييل الصفحة -->
    <footer class="bg-light text-center py-3 mt-5 no-print">
        <div class="container">
            <p class="mb-0 text-muted">
                &copy; {{ moment().format('YYYY') if moment else '2024' }} نظام إدارة السائقين والمركبات. 
                جميع الحقوق محفوظة.
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript مخصص -->
    <script>
        // تهيئة moment.js للعربية
        if (typeof moment !== 'undefined') {
            moment.locale('ar');
        }
        
        // تحديث عداد الإشعارات
        function updateNotificationCount() {
            fetch('/api/notifications?unread_only=true')
                .then(response => response.json())
                .then(data => {
                    const badge = document.querySelector('.notification-badge');
                    if (badge) {
                        const count = data.length;
                        if (count > 0) {
                            badge.textContent = count > 99 ? '99+' : count;
                            badge.style.display = 'inline';
                        } else {
                            badge.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث عداد الإشعارات:', error);
                });
        }
        
        // تحديث عداد الإشعارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationCount();
            
            // تحديث العداد كل دقيقة
            setInterval(updateNotificationCount, 60000);
        });
        
        // وظائف مساعدة للتفاعل
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        function showLoading(element) {
            if (element) {
                element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                element.disabled = true;
            }
        }
        
        function hideLoading(element, originalText) {
            if (element) {
                element.innerHTML = originalText;
                element.disabled = false;
            }
        }
        
        // تحسين تجربة المستخدم للنماذج
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (!this.disabled && !this.classList.contains('btn-outline-secondary')) {
                        this.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 100);
                    }
                });
            });
            
            // تحسين النماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        showLoading(submitBtn);
                    }
                });
            });
        });
        
        // وظيفة الطباعة
        function printPage() {
            window.print();
        }
        
        // وظيفة تصدير البيانات
        function exportData(format, url) {
            const link = document.createElement('a');
            link.href = url + (url.includes('?') ? '&' : '?') + 'format=' + format;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>