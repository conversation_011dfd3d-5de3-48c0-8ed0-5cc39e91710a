#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مجدول المهام التلقائية
Automated Task Scheduler

يحتوي على مهام تلقائية مثل:
- النسخ الاحتياطية التلقائية
- إرسال التقارير اليومية
- تنظيف الملفات القديمة
- فحص التنبيهات
"""

import schedule
import time
import os
import shutil
import logging
from datetime import datetime, timedelta
from app import app, db, Driver, Payment, Expense, Vehicle
from notifications import notification_manager

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskScheduler:
    """مجدول المهام"""
    
    def __init__(self):
        self.app = app
        self.running = False
        
        # إنشاء مجلدات اللوجز إذا لم تكن موجودة
        os.makedirs('logs', exist_ok=True)
        os.makedirs('backups', exist_ok=True)
        os.makedirs('exports', exist_ok=True)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية تلقائية"""
        try:
            logger.info("بدء إنشاء النسخة الاحتياطية التلقائية...")
            
            with self.app.app_context():
                # اسم ملف النسخة الاحتياطية
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_filename = f'auto_backup_{timestamp}.db'
                backup_path = os.path.join('backups', backup_filename)
                
                # نسخ قاعدة البيانات
                db_path = 'drivers.db'  # أو المسار الصحيح لقاعدة البيانات
                if os.path.exists(db_path):
                    shutil.copy2(db_path, backup_path)
                    
                    # إضافة إشعار
                    if notification_manager:
                        notification_manager.add_notification(
                            title='تم إنشاء نسخة احتياطية تلقائية',
                            message=f'تم إنشاء النسخة الاحتياطية: {backup_filename}',
                            type='success'
                        )
                    
                    logger.info(f"تم إنشاء النسخة الاحتياطية: {backup_filename}")
                else:
                    logger.error("ملف قاعدة البيانات غير موجود")
                    
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            
            if notification_manager:
                notification_manager.add_notification(
                    title='فشل في إنشاء النسخة الاحتياطية',
                    message=f'خطأ: {str(e)}',
                    type='error'
                )
    
    def cleanup_old_backups(self, days=30):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            logger.info(f"تنظيف النسخ الاحتياطية الأقدم من {days} يوم...")
            
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            backup_dir = 'backups'
            if os.path.exists(backup_dir):
                for filename in os.listdir(backup_dir):
                    if filename.endswith('.db'):
                        filepath = os.path.join(backup_dir, filename)
                        file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                        
                        if file_time < cutoff_date:
                            os.remove(filepath)
                            deleted_count += 1
                            logger.info(f"تم حذف النسخة الاحتياطية القديمة: {filename}")
            
            if deleted_count > 0:
                if notification_manager:
                    notification_manager.add_notification(
                        title='تنظيف النسخ الاحتياطية',
                        message=f'تم حذف {deleted_count} نسخة احتياطية قديمة',
                        type='info'
                    )
                    
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
    
    def cleanup_old_exports(self, hours=24):
        """تنظيف ملفات التصدير القديمة"""
        try:
            logger.info(f"تنظيف ملفات التصدير الأقدم من {hours} ساعة...")
            
            cutoff_date = datetime.now() - timedelta(hours=hours)
            deleted_count = 0
            
            export_dir = 'exports'
            if os.path.exists(export_dir):
                for filename in os.listdir(export_dir):
                    filepath = os.path.join(export_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    
                    if file_time < cutoff_date:
                        os.remove(filepath)
                        deleted_count += 1
                        logger.info(f"تم حذف ملف التصدير القديم: {filename}")
            
            if deleted_count > 0:
                logger.info(f"تم حذف {deleted_count} ملف تصدير قديم")
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف ملفات التصدير: {e}")
    
    def send_daily_report(self):
        """إرسال التقرير اليومي"""
        try:
            logger.info("إرسال التقرير اليومي...")
            
            with self.app.app_context():
                # جمع الإحصائيات
                stats = {
                    'drivers_count': Driver.query.count(),
                    'vehicles_count': Vehicle.query.count(),
                    'positive_balance_count': Driver.query.filter(Driver.balance > 0).count(),
                    'negative_balance_count': Driver.query.filter(Driver.balance < 0).count(),
                    'today_payments': Payment.query.filter(
                        Payment.created_at >= datetime.now().date()
                    ).count(),
                    'today_amount': db.session.query(func.sum(Payment.amount)).filter(
                        Payment.created_at >= datetime.now().date()
                    ).scalar() or 0
                }
                
                # إرسال التقرير (يحتاج إعداد البريد الإلكتروني)
                admin_email = app.config.get('ADMIN_EMAIL')
                if admin_email and notification_manager:
                    success = notification_manager.send_daily_report(admin_email, stats)
                    if success:
                        logger.info("تم إرسال التقرير اليومي بنجاح")
                    else:
                        logger.warning("فشل في إرسال التقرير اليومي")
                else:
                    logger.info("لم يتم تكوين البريد الإلكتروني للمدير")
                    
        except Exception as e:
            logger.error(f"خطأ في إرسال التقرير اليومي: {e}")
    
    def check_system_alerts(self):
        """فحص تنبيهات النظام"""
        try:
            logger.info("فحص تنبيهات النظام...")
            
            with self.app.app_context():
                # فحص الأرصدة السالبة
                negative_balance_drivers = Driver.query.filter(Driver.balance < 0).all()
                
                for driver in negative_balance_drivers:
                    if notification_manager:
                        # التحقق من عدم وجود تنبيه مشابه في آخر 24 ساعة
                        recent_notifications = notification_manager.get_notifications()
                        has_recent_alert = any(
                            n.get('data', {}).get('driver_id') == driver.id and
                            n.get('type') == 'warning' and
                            (datetime.now() - datetime.fromisoformat(n['created_at'])).days < 1
                            for n in recent_notifications
                        )
                        
                        if not has_recent_alert:
                            notification_manager.add_notification(
                                title='تنبيه: رصيد سالب',
                                message=f'السائق {driver.name} لديه رصيد سالب: {driver.balance:.2f} ريال',
                                type='warning',
                                data={'driver_id': driver.id, 'balance': driver.balance}
                            )
                
                # فحص المدفوعات الكبيرة اليوم
                today_large_payments = Payment.query.filter(
                    Payment.created_at >= datetime.now().date(),
                    Payment.amount > 5000
                ).all()
                
                for payment in today_large_payments:
                    if notification_manager:
                        notification_manager.add_notification(
                            title='مدفوعة كبيرة',
                            message=f'مدفوعة كبيرة: {payment.amount:.2f} ريال للسائق {payment.driver.name}',
                            type='info',
                            data={'payment_id': payment.id, 'amount': payment.amount}
                        )
                
                logger.info("تم فحص تنبيهات النظام")
                
        except Exception as e:
            logger.error(f"خطأ في فحص تنبيهات النظام: {e}")
    
    def cleanup_old_logs(self, days=30):
        """تنظيف ملفات السجلات القديمة"""
        try:
            logger.info(f"تنظيف ملفات السجلات الأقدم من {days} يوم...")
            
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = 0
            
            logs_dir = 'logs'
            if os.path.exists(logs_dir):
                for filename in os.listdir(logs_dir):
                    if filename.endswith('.log'):
                        filepath = os.path.join(logs_dir, filename)
                        file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                        
                        if file_time < cutoff_date:
                            os.remove(filepath)
                            deleted_count += 1
                            logger.info(f"تم حذف ملف السجل القديم: {filename}")
            
            if deleted_count > 0:
                logger.info(f"تم حذف {deleted_count} ملف سجل قديم")
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف ملفات السجلات: {e}")
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            logger.info("تحسين قاعدة البيانات...")
            
            with self.app.app_context():
                # تنفيذ VACUUM لـ SQLite
                db.session.execute(text("VACUUM"))
                db.session.commit()
                
                # إعادة فهرسة الجداول
                db.session.execute(text("REINDEX"))
                db.session.commit()
                
                logger.info("تم تحسين قاعدة البيانات بنجاح")
                
                if notification_manager:
                    notification_manager.add_notification(
                        title='تحسين قاعدة البيانات',
                        message='تم تحسين قاعدة البيانات وإعادة الفهرسة بنجاح',
                        type='success'
                    )
                    
        except Exception as e:
            logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
    
    def setup_schedule(self):
        """إعداد جدولة المهام"""
        logger.info("إعداد جدولة المهام...")
        
        # نسخة احتياطية يومية في الساعة 2:00 صباحاً
        schedule.every().day.at("02:00").do(self.create_backup)
        
        # تنظيف النسخ الاحتياطية القديمة أسبوعياً
        schedule.every().sunday.at("03:00").do(self.cleanup_old_backups)
        
        # تنظيف ملفات التصدير يومياً
        schedule.every().day.at("04:00").do(self.cleanup_old_exports)
        
        # إرسال التقرير اليومي في الساعة 8:00 صباحاً
        schedule.every().day.at("08:00").do(self.send_daily_report)
        
        # فحص التنبيهات كل ساعة
        schedule.every().hour.do(self.check_system_alerts)
        
        # تنظيف السجلات القديمة شهرياً
        schedule.every().month.do(self.cleanup_old_logs)
        
        # تحسين قاعدة البيانات أسبوعياً
        schedule.every().sunday.at("05:00").do(self.optimize_database)
        
        # تنظيف الإشعارات القديمة يومياً
        if notification_manager:
            schedule.every().day.at("06:00").do(
                lambda: notification_manager.cleanup_old_notifications()
            )
        
        logger.info("تم إعداد جدولة المهام بنجاح")
    
    def run(self):
        """تشغيل المجدول"""
        self.setup_schedule()
        self.running = True
        
        logger.info("بدء تشغيل مجدول المهام...")
        logger.info("المهام المجدولة:")
        for job in schedule.jobs:
            logger.info(f"  - {job}")
        
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
                
        except KeyboardInterrupt:
            logger.info("تم إيقاف مجدول المهام بواسطة المستخدم")
        except Exception as e:
            logger.error(f"خطأ في مجدول المهام: {e}")
        finally:
            self.running = False
            logger.info("تم إيقاف مجدول المهام")
    
    def stop(self):
        """إيقاف المجدول"""
        self.running = False
        logger.info("تم طلب إيقاف مجدول المهام")

def run_scheduler():
    """تشغيل المجدول كعملية منفصلة"""
    scheduler = TaskScheduler()
    scheduler.run()

if __name__ == "__main__":
    # تشغيل المجدول
    run_scheduler()